2025-08-05 13:42:10,682 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 13:42:10,683 - __main__ - INFO - 📐 滚动配置: 步数=57, 联系人高度=48px
2025-08-05 13:42:10,685 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:42:10,687 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 160x28
2025-08-05 13:42:10,688 - __main__ - WARNING - ⚠️ 窗口尺寸过小 (160x28)，尝试恢复窗口...
2025-08-05 13:42:11,229 - __main__ - INFO - 🔄 恢复后窗口尺寸: 700x1000
2025-08-05 13:42:11,232 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:42:11,233 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 13:42:11,234 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:42:11,236 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 13:42:11,236 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:42:11,236 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 13:42:11,737 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 13:42:11,738 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 13:42:12,496 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:42:13,755 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:42:15,011 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:42:15,513 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 13:42:15,513 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 13:42:17,199 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 13:42:17,225 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 13:42:17,226 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:17,431 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:42:18,749 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 13:42:18,750 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 13:42:20,437 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 13:42:20,438 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 13:42:20,439 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:20,643 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:42:21,944 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 13:42:21,944 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 13:42:23,620 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 13:42:23,620 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 13:42:23,621 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:23,823 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:42:25,125 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 13:42:25,125 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 13:42:26,804 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 13:42:26,805 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 13:42:26,806 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:27,009 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:42:28,310 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 13:42:28,310 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 13:42:29,989 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 13:42:29,990 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 13:42:29,991 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:30,194 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:42:31,495 - __main__ - INFO - 📋 处理第 6/100 个联系人
2025-08-05 13:42:31,496 - __main__ - INFO - 🖱️ 点击联系人位置 6: (1470, 176)
2025-08-05 13:42:33,170 - __main__ - INFO - ✅ 成功点击位置 6: (1470, 176)
2025-08-05 13:42:33,170 - __main__ - INFO - ✅ 成功点击第 6 个联系人
2025-08-05 13:42:33,171 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:33,373 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:42:34,675 - __main__ - INFO - 📋 处理第 7/100 个联系人
2025-08-05 13:42:34,675 - __main__ - INFO - 🖱️ 点击联系人位置 7: (1470, 176)
2025-08-05 13:42:36,353 - __main__ - INFO - ✅ 成功点击位置 7: (1470, 176)
2025-08-05 13:42:36,353 - __main__ - INFO - ✅ 成功点击第 7 个联系人
2025-08-05 13:42:36,354 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:36,556 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:42:37,857 - __main__ - INFO - 📋 处理第 8/100 个联系人
2025-08-05 13:42:37,858 - __main__ - INFO - 🖱️ 点击联系人位置 8: (1470, 176)
2025-08-05 13:42:39,537 - __main__ - INFO - ✅ 成功点击位置 8: (1470, 176)
2025-08-05 13:42:39,537 - __main__ - INFO - ✅ 成功点击第 8 个联系人
2025-08-05 13:42:39,537 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:39,739 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:42:41,041 - __main__ - INFO - 📋 处理第 9/100 个联系人
2025-08-05 13:42:41,041 - __main__ - INFO - 🖱️ 点击联系人位置 9: (1470, 176)
2025-08-05 13:42:42,720 - __main__ - INFO - ✅ 成功点击位置 9: (1470, 176)
2025-08-05 13:42:42,720 - __main__ - INFO - ✅ 成功点击第 9 个联系人
2025-08-05 13:42:42,720 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:42,923 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:42:44,224 - __main__ - INFO - 📋 处理第 10/100 个联系人
2025-08-05 13:42:44,224 - __main__ - INFO - 🖱️ 点击联系人位置 10: (1470, 176)
2025-08-05 13:42:45,935 - __main__ - INFO - ✅ 成功点击位置 10: (1470, 176)
2025-08-05 13:42:45,936 - __main__ - INFO - ✅ 成功点击第 10 个联系人
2025-08-05 13:42:45,936 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:42:46,139 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:43:18,032 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 13:43:18,033 - __main__ - INFO - 📐 滚动配置: 步数=57, 联系人高度=48px
2025-08-05 13:43:18,033 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:43:18,035 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 13:43:18,036 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:43:18,036 - __main__ - WARNING - ⚠️ 窗口枚举异常: (183, 'EnumWindows', '当文件已存在时，无法创建该文件。')
2025-08-05 13:43:18,036 - __main__ - INFO - ✅ 通过标题找到窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:43:18,039 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 13:43:18,039 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:43:18,040 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 13:43:18,041 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:43:18,043 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 13:43:18,545 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 13:43:18,546 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 13:43:19,303 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:43:20,560 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:43:21,817 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:43:22,318 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 13:43:22,319 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 13:43:24,005 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 13:43:24,005 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 13:43:24,005 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:43:24,208 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:44:54,476 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 13:44:54,477 - __main__ - INFO - 📐 滚动配置: 步数=57, 联系人高度=48px
2025-08-05 13:44:54,478 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:44:54,480 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 13:44:54,480 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:44:54,481 - __main__ - WARNING - ⚠️ 窗口枚举异常: (183, 'EnumWindows', '当文件已存在时，无法创建该文件。')
2025-08-05 13:44:54,481 - __main__ - INFO - ✅ 通过标题找到窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:44:54,483 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 13:44:54,484 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:44:54,485 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 13:44:54,486 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:44:54,486 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 13:44:54,988 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 13:44:54,988 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 13:44:55,745 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:44:57,003 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:44:58,261 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:44:58,762 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 13:44:58,763 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 13:45:00,444 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 13:45:00,444 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 13:45:00,444 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:45:00,646 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:45:01,948 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 13:45:01,949 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 13:45:03,638 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 13:45:03,646 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 13:45:03,647 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:45:03,849 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:54:52,276 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 13:54:52,277 - __main__ - INFO - 📐 滚动配置: 步数=57, 联系人高度=48px
2025-08-05 13:54:52,277 - __main__ - INFO - 📤 发送消息按钮位置: (1562, 517)
2025-08-05 13:54:52,278 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:54:52,281 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 160x28
2025-08-05 13:54:52,282 - __main__ - WARNING - ⚠️ 窗口尺寸过小 (160x28)，尝试恢复窗口...
2025-08-05 13:54:52,846 - __main__ - INFO - 🔄 恢复后窗口尺寸: 700x1000
2025-08-05 13:54:52,846 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:54:52,848 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 13:54:52,848 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:54:52,849 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 13:54:52,850 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:54:52,850 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 13:54:53,352 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 13:54:53,352 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 13:54:54,111 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:54:55,370 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:54:56,666 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:54:57,167 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 13:54:57,167 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 13:54:58,841 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 13:54:58,842 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 13:54:58,843 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:54:59,045 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:55:00,349 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 13:55:00,349 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 13:55:01,625 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 13:55:02,126 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 13:55:02,126 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 13:55:03,807 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 13:55:03,808 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 13:55:03,808 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:55:04,011 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:55:05,311 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 13:55:05,312 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 13:55:06,574 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 13:55:07,075 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 13:55:07,076 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 13:55:08,757 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 13:55:08,758 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 13:55:08,758 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:55:08,961 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:55:10,262 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 13:55:10,262 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 13:55:11,541 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 13:55:12,042 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 13:55:12,042 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 13:55:13,713 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 13:55:13,714 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 13:55:13,714 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:55:13,916 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:55:15,218 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 13:55:15,220 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 13:55:16,491 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 13:55:17,021 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 13:55:17,059 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 13:55:18,807 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 13:55:18,807 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 13:55:18,808 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:55:19,010 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:55:20,311 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 13:55:20,311 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 13:55:21,573 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 13:55:22,074 - __main__ - INFO - 📋 处理第 6/100 个联系人
2025-08-05 13:55:22,074 - __main__ - INFO - 🖱️ 点击联系人位置 6: (1470, 176)
2025-08-05 13:55:23,744 - __main__ - INFO - ✅ 成功点击位置 6: (1470, 176)
2025-08-05 13:55:23,745 - __main__ - INFO - ✅ 成功点击第 6 个联系人
2025-08-05 13:55:23,746 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:55:23,947 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:55:25,249 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 13:55:25,250 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 13:55:29,311 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 13:55:29,311 - __main__ - INFO - 📐 滚动配置: 步数=57, 联系人高度=48px
2025-08-05 13:55:29,312 - __main__ - INFO - 📤 发送消息按钮位置: (1562, 517)
2025-08-05 13:55:29,313 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:55:29,315 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 13:55:29,315 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:55:29,316 - __main__ - WARNING - ⚠️ 窗口枚举异常: (183, 'EnumWindows', '当文件已存在时，无法创建该文件。')
2025-08-05 13:55:29,316 - __main__ - INFO - ✅ 通过标题找到窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:55:29,329 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 13:55:29,330 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 13:55:29,332 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 13:55:29,339 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 13:55:29,341 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 13:55:29,843 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 13:55:29,843 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 13:55:30,598 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:55:31,857 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:55:33,115 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 13:55:33,616 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 13:55:33,617 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 13:55:35,391 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 13:55:35,391 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 13:55:35,391 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:55:35,593 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:55:36,894 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 13:55:36,894 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 13:55:38,172 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 13:55:38,672 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 13:55:38,673 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 13:55:40,355 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 13:55:40,356 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 13:55:40,357 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:55:40,568 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:55:41,869 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 13:55:41,870 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 13:55:43,139 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 13:55:43,639 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 13:55:43,640 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 13:55:45,312 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 13:55:45,312 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 13:55:45,313 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 13:55:45,515 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 13:55:46,817 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 13:55:46,818 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 13:55:48,107 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 13:55:48,618 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 13:55:48,620 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 13:55:48,744 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 14:05:32,868 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:05:32,868 - __main__ - INFO - 📐 滚动配置: 步数=57, 联系人高度=48px
2025-08-05 14:05:32,869 - __main__ - INFO - 📤 发送消息按钮位置: (1562, 517)
2025-08-05 14:05:32,870 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:05:32,872 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:05:32,872 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:05:32,872 - __main__ - WARNING - ⚠️ 窗口枚举异常: (183, 'EnumWindows', '当文件已存在时，无法创建该文件。')
2025-08-05 14:05:32,873 - __main__ - INFO - ✅ 通过标题找到窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:05:32,876 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:05:32,877 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:05:32,878 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:05:32,879 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:05:32,880 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:05:33,382 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:05:33,382 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:05:34,138 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:05:35,396 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:05:36,654 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:05:37,155 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:05:37,155 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:05:38,835 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:05:38,835 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:05:38,836 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:05:39,038 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 14:05:40,339 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:05:40,340 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 14:05:41,599 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 14:05:42,100 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:05:42,100 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:05:43,770 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:05:43,770 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:05:43,771 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:05:43,973 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 14:05:45,275 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:05:45,275 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 14:05:46,551 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 14:05:47,052 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:05:47,052 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:05:48,734 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:05:48,734 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:05:48,735 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:05:48,936 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 14:05:50,238 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:05:50,238 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 14:05:51,500 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 14:05:52,001 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:05:52,001 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:05:53,672 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:05:53,672 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:05:53,673 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:05:53,875 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 14:05:55,176 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:05:55,176 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 14:05:56,451 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 14:05:56,962 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:05:57,001 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:05:58,683 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:05:58,684 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:05:58,684 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:05:58,886 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 14:06:00,188 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:06:00,188 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 517)
2025-08-05 14:06:01,451 - __main__ - INFO - ✅ 成功点击发送消息按钮
2025-08-05 14:06:01,952 - __main__ - INFO - 📋 处理第 6/100 个联系人
2025-08-05 14:06:01,953 - __main__ - INFO - 🖱️ 点击联系人位置 6: (1470, 176)
2025-08-05 14:06:03,633 - __main__ - INFO - ✅ 成功点击位置 6: (1470, 176)
2025-08-05 14:06:03,634 - __main__ - INFO - ✅ 成功点击第 6 个联系人
2025-08-05 14:06:03,634 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:06:03,850 - __main__ - INFO - 📜 向下滚动 57 步
2025-08-05 14:11:21,567 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:11:21,567 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:11:21,567 - __main__ - INFO - 📤 发送消息按钮: 动态计算位置（基于联系人位置相对偏移）
2025-08-05 14:11:21,573 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:11:21,579 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:11:21,585 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:11:21,597 - __main__ - WARNING - ⚠️ 窗口枚举异常: (183, 'EnumWindows', '当文件已存在时，无法创建该文件。')
2025-08-05 14:11:21,619 - __main__ - INFO - ✅ 通过标题找到窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:11:21,670 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:11:21,679 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:11:21,685 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:11:21,689 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:11:21,690 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:11:22,194 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:11:22,195 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:11:22,963 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:11:24,220 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:11:25,479 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:11:25,980 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:11:25,980 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:11:27,664 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:11:27,665 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:11:27,665 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:11:27,868 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:11:29,170 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:11:29,170 - __main__ - INFO - 📤 基于联系人位置 (1470, 176) 智能定位发送消息按钮
2025-08-05 14:11:29,170 - __main__ - INFO - 📤 尝试候选位置 1: (1610, 171)
2025-08-05 14:11:30,064 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1610, 171)
2025-08-05 14:11:30,565 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:11:30,565 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:11:32,247 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:11:32,247 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:11:32,248 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:11:32,450 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:11:33,751 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:11:33,751 - __main__ - INFO - 📤 基于联系人位置 (1470, 176) 智能定位发送消息按钮
2025-08-05 14:11:33,752 - __main__ - INFO - 📤 尝试候选位置 1: (1610, 171)
2025-08-05 14:11:34,645 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1610, 171)
2025-08-05 14:11:35,146 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:11:35,147 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:11:36,831 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:11:36,861 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:11:36,896 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:11:37,137 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:11:38,451 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:11:38,451 - __main__ - INFO - 📤 基于联系人位置 (1470, 176) 智能定位发送消息按钮
2025-08-05 14:11:38,451 - __main__ - INFO - 📤 尝试候选位置 1: (1610, 171)
2025-08-05 14:11:39,346 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1610, 171)
2025-08-05 14:11:39,846 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:11:39,847 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:11:41,520 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:11:41,521 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:11:41,521 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:11:41,724 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:11:43,027 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:11:43,028 - __main__ - INFO - 📤 基于联系人位置 (1470, 176) 智能定位发送消息按钮
2025-08-05 14:11:43,028 - __main__ - INFO - 📤 尝试候选位置 1: (1610, 171)
2025-08-05 14:11:43,929 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1610, 171)
2025-08-05 14:11:44,430 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:11:44,430 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:11:46,112 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:11:46,113 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:11:46,113 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:11:46,316 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:11:47,617 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:11:47,617 - __main__ - INFO - 📤 基于联系人位置 (1470, 176) 智能定位发送消息按钮
2025-08-05 14:11:47,618 - __main__ - INFO - 📤 尝试候选位置 1: (1610, 171)
2025-08-05 14:11:48,534 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1610, 171)
2025-08-05 14:11:49,035 - __main__ - INFO - 📋 处理第 6/100 个联系人
2025-08-05 14:11:49,036 - __main__ - INFO - 🖱️ 点击联系人位置 6: (1470, 176)
2025-08-05 14:11:49,535 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 14:17:28,422 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:17:28,423 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:17:28,424 - __main__ - INFO - 📤 发送消息按钮: 动态计算位置（基于联系人位置相对偏移）
2025-08-05 14:17:28,426 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:17:28,428 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:17:28,428 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:17:28,429 - __main__ - WARNING - ⚠️ 窗口枚举异常: (183, 'EnumWindows', '当文件已存在时，无法创建该文件。')
2025-08-05 14:17:28,430 - __main__ - INFO - ✅ 通过标题找到窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:17:28,442 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:17:28,445 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:17:28,451 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:17:28,461 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:17:28,466 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:17:28,983 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:17:28,984 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:17:29,742 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:17:31,080 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:17:32,339 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:17:32,840 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:17:32,841 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:17:34,525 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:17:34,526 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:17:34,526 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:17:34,729 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:17:36,030 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:17:36,031 - __main__ - INFO - 📤 基于联系人位置 (1470, 176) 智能定位发送消息按钮
2025-08-05 14:17:36,031 - __main__ - INFO - 📤 尝试候选位置 1: (1562, 639)
2025-08-05 14:17:36,925 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1562, 639)
2025-08-05 14:17:37,426 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:17:37,427 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:17:39,096 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:17:39,097 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:17:39,097 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:17:39,300 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:17:40,601 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:17:40,603 - __main__ - INFO - 📤 基于联系人位置 (1470, 176) 智能定位发送消息按钮
2025-08-05 14:17:40,603 - __main__ - INFO - 📤 尝试候选位置 1: (1562, 639)
2025-08-05 14:17:41,490 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1562, 639)
2025-08-05 14:17:41,991 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:17:41,991 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:17:43,723 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:17:43,724 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:17:43,724 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:17:43,936 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:17:45,283 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:17:45,283 - __main__ - INFO - 📤 基于联系人位置 (1470, 176) 智能定位发送消息按钮
2025-08-05 14:17:45,284 - __main__ - INFO - 📤 尝试候选位置 1: (1562, 639)
2025-08-05 14:17:46,173 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1562, 639)
2025-08-05 14:17:46,673 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:17:46,674 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:17:48,357 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:17:48,357 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:17:48,358 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:17:48,560 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:17:49,861 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:17:49,861 - __main__ - INFO - 📤 基于联系人位置 (1470, 176) 智能定位发送消息按钮
2025-08-05 14:17:49,861 - __main__ - INFO - 📤 尝试候选位置 1: (1562, 639)
2025-08-05 14:17:50,756 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1562, 639)
2025-08-05 14:17:51,257 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:17:51,257 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:17:52,962 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 14:23:13,253 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:23:13,253 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:23:13,254 - __main__ - INFO - 📤 发送消息按钮: 动态计算位置（基于联系人位置相对偏移）
2025-08-05 14:23:13,254 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:23:13,257 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 160x28
2025-08-05 14:23:13,257 - __main__ - WARNING - ⚠️ 窗口尺寸过小 (160x28)，尝试恢复窗口...
2025-08-05 14:23:13,796 - __main__ - INFO - 🔄 恢复后窗口尺寸: 700x1000
2025-08-05 14:23:13,796 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:23:13,802 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:23:13,803 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:23:13,806 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:23:13,807 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:23:13,808 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:23:14,308 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:23:14,310 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:23:15,068 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:23:16,325 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:23:17,585 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:23:18,086 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:23:18,087 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:23:19,769 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:23:19,770 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:23:19,770 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:23:19,973 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:23:21,276 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:23:21,276 - __main__ - INFO - 📤 点击发送消息按钮（界面底部固定位置）
2025-08-05 14:23:21,276 - __main__ - INFO - 📤 尝试发送消息按钮位置 1: (1562, 924)
2025-08-05 14:23:22,160 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1562, 924)
2025-08-05 14:23:22,660 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:23:22,661 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:23:24,339 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:23:24,340 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:23:24,340 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:23:24,542 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:23:25,844 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:23:25,844 - __main__ - INFO - 📤 点击发送消息按钮（界面底部固定位置）
2025-08-05 14:23:25,844 - __main__ - INFO - 📤 尝试发送消息按钮位置 1: (1562, 924)
2025-08-05 14:23:26,735 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1562, 924)
2025-08-05 14:23:27,236 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:23:27,237 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:23:28,919 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:23:28,920 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:23:28,920 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:23:29,122 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:23:30,424 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:23:30,425 - __main__ - INFO - 📤 点击发送消息按钮（界面底部固定位置）
2025-08-05 14:23:30,425 - __main__ - INFO - 📤 尝试发送消息按钮位置 1: (1562, 924)
2025-08-05 14:23:31,318 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1562, 924)
2025-08-05 14:23:31,819 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:23:31,820 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:23:33,519 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:23:33,520 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:23:33,520 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:23:33,722 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:23:35,024 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:23:35,024 - __main__ - INFO - 📤 点击发送消息按钮（界面底部固定位置）
2025-08-05 14:23:35,024 - __main__ - INFO - 📤 尝试发送消息按钮位置 1: (1562, 924)
2025-08-05 14:23:35,919 - __main__ - INFO - ✅ 成功点击发送消息按钮位置 1: (1562, 924)
2025-08-05 14:23:36,420 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:23:36,420 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:23:38,108 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:23:38,109 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:23:38,109 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:23:38,495 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 14:29:14,162 - __main__ - INFO - ✅ UI Automation 初始化成功
2025-08-05 14:29:14,163 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:29:14,163 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:29:14,163 - __main__ - INFO - 📤 发送消息按钮: 智能UI元素定位（基于UI Automation）
2025-08-05 14:29:14,165 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:14,174 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:14,176 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:14,193 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:29:14,194 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:14,196 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:14,197 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:14,197 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:29:14,703 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:29:14,704 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:29:15,459 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:29:16,716 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:29:18,018 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:29:18,519 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:29:18,519 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:29:20,198 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:29:20,198 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:29:20,199 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:29:20,401 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:29:21,702 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:29:21,702 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:29:21,702 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:21,704 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:21,705 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:21,705 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:29:21,887 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:29:21,914 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:29:21,930 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:29:22,965 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:29:23,466 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:29:23,467 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:29:25,153 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:29:25,153 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:29:25,153 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:29:25,356 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:29:26,657 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:29:26,657 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:29:26,657 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:26,661 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:26,661 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:26,662 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:29:26,725 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:29:26,727 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:29:26,727 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:29:27,798 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:29:28,299 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:29:28,299 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:29:29,981 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:29:29,982 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:29:29,982 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:29:30,184 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:29:31,485 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:29:31,486 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:29:31,487 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:31,489 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:31,490 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:31,496 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:29:31,846 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:29:31,847 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:29:31,847 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:29:32,897 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:29:33,398 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:29:33,398 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:29:35,080 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:29:35,081 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:29:35,081 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:29:35,283 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:29:36,587 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:29:36,587 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:29:36,588 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:36,589 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:36,590 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:36,590 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:29:36,655 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:29:36,664 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:29:36,683 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:29:37,730 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:29:38,230 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:29:38,231 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:29:39,934 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:29:39,934 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:29:39,934 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:29:40,137 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:29:41,438 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:29:41,439 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:29:41,439 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:41,442 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:41,443 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:41,443 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:29:41,516 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:29:41,517 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:29:41,517 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:29:42,599 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:29:43,100 - __main__ - INFO - 📋 处理第 6/100 个联系人
2025-08-05 14:29:43,100 - __main__ - INFO - 🖱️ 点击联系人位置 6: (1470, 176)
2025-08-05 14:29:44,779 - __main__ - INFO - ✅ 成功点击位置 6: (1470, 176)
2025-08-05 14:29:44,779 - __main__ - INFO - ✅ 成功点击第 6 个联系人
2025-08-05 14:29:44,780 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:29:44,983 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:29:46,286 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:29:46,286 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:29:46,286 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:46,288 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:46,288 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:46,288 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:29:46,363 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:29:46,364 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:29:46,364 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:29:47,413 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:29:47,913 - __main__ - INFO - 📋 处理第 7/100 个联系人
2025-08-05 14:29:47,914 - __main__ - INFO - 🖱️ 点击联系人位置 7: (1470, 176)
2025-08-05 14:29:49,595 - __main__ - INFO - ✅ 成功点击位置 7: (1470, 176)
2025-08-05 14:29:49,596 - __main__ - INFO - ✅ 成功点击第 7 个联系人
2025-08-05 14:29:49,596 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:29:49,798 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:29:51,099 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:29:51,099 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:29:51,100 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:51,101 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:51,102 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:51,102 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:29:51,184 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:29:51,185 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:29:51,186 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:29:52,228 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:29:52,729 - __main__ - INFO - 📋 处理第 8/100 个联系人
2025-08-05 14:29:52,730 - __main__ - INFO - 🖱️ 点击联系人位置 8: (1470, 176)
2025-08-05 14:29:54,412 - __main__ - INFO - ✅ 成功点击位置 8: (1470, 176)
2025-08-05 14:29:54,412 - __main__ - INFO - ✅ 成功点击第 8 个联系人
2025-08-05 14:29:54,412 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:29:54,614 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:29:55,916 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:29:55,916 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:29:55,916 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:29:55,918 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:29:55,918 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:29:55,919 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:29:55,993 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:29:55,995 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:29:55,995 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:29:57,049 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:29:57,568 - __main__ - INFO - 📋 处理第 9/100 个联系人
2025-08-05 14:29:57,569 - __main__ - INFO - 🖱️ 点击联系人位置 9: (1470, 176)
2025-08-05 14:29:59,413 - __main__ - INFO - ✅ 成功点击位置 9: (1470, 176)
2025-08-05 14:29:59,413 - __main__ - INFO - ✅ 成功点击第 9 个联系人
2025-08-05 14:29:59,414 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:29:59,633 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:30:01,001 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:30:01,002 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:30:01,002 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:30:01,003 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:30:01,004 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:30:01,004 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:30:01,073 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:30:01,076 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:30:01,077 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:30:02,134 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:30:02,635 - __main__ - INFO - 📋 处理第 10/100 个联系人
2025-08-05 14:30:02,636 - __main__ - INFO - 🖱️ 点击联系人位置 10: (1470, 176)
2025-08-05 14:30:04,312 - __main__ - INFO - ✅ 成功点击位置 10: (1470, 176)
2025-08-05 14:30:04,312 - __main__ - INFO - ✅ 成功点击第 10 个联系人
2025-08-05 14:30:04,313 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:30:04,515 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:30:05,816 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:30:05,817 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:30:05,817 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:30:05,819 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:30:05,819 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:30:05,820 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:30:05,885 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:30:05,886 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:30:05,886 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:30:06,950 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:30:07,451 - __main__ - INFO - 📋 处理第 11/100 个联系人
2025-08-05 14:30:07,451 - __main__ - INFO - 🖱️ 点击联系人位置 11: (1470, 176)
2025-08-05 14:30:09,128 - __main__ - INFO - ✅ 成功点击位置 11: (1470, 176)
2025-08-05 14:30:09,128 - __main__ - INFO - ✅ 成功点击第 11 个联系人
2025-08-05 14:30:09,128 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:30:09,330 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:30:10,631 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:30:10,631 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:30:10,632 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:30:10,634 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:30:10,634 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:30:10,635 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:30:10,706 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:30:10,707 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:30:10,708 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:30:11,743 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:30:12,244 - __main__ - INFO - 📋 处理第 12/100 个联系人
2025-08-05 14:30:12,245 - __main__ - INFO - 🖱️ 点击联系人位置 12: (1470, 176)
2025-08-05 14:30:13,945 - __main__ - INFO - ✅ 成功点击位置 12: (1470, 176)
2025-08-05 14:30:13,946 - __main__ - INFO - ✅ 成功点击第 12 个联系人
2025-08-05 14:30:13,946 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:30:14,155 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:31:52,713 - __main__ - INFO - ✅ UI Automation 初始化成功
2025-08-05 14:31:52,714 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:31:52,722 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:31:52,723 - __main__ - INFO - 📤 发送消息按钮: 智能UI元素定位（基于UI Automation）
2025-08-05 14:31:52,724 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:31:52,726 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 160x28
2025-08-05 14:31:52,727 - __main__ - WARNING - ⚠️ 窗口尺寸过小 (160x28)，尝试恢复窗口...
2025-08-05 14:31:53,264 - __main__ - INFO - 🔄 恢复后窗口尺寸: 700x1000
2025-08-05 14:31:53,278 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:31:53,441 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:31:53,442 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:31:53,444 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:31:53,445 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:31:53,447 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:31:53,948 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:31:53,949 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:31:54,706 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:31:55,964 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:31:57,221 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:31:57,722 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:31:57,723 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:31:59,404 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:31:59,405 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:31:59,406 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:31:59,608 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:32:00,909 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:32:00,910 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:32:00,910 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:32:00,912 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:32:00,912 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:32:00,913 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:32:00,913 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:32:00,913 - __main__ - INFO - 🔍 策略2: 使用区域扫描查找发送消息按钮
2025-08-05 14:32:00,913 - __main__ - INFO - 📤 点击发送消息按钮: (1686, 900)
2025-08-05 14:32:01,957 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1686, 900)
2025-08-05 14:32:02,458 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:32:02,460 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:32:04,139 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:32:04,139 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:32:04,139 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:32:04,343 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:32:05,644 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:32:05,645 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:32:05,645 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:32:05,647 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:32:05,647 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:32:05,647 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:32:05,648 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:32:05,648 - __main__ - INFO - 🔍 策略2: 使用区域扫描查找发送消息按钮
2025-08-05 14:32:05,652 - __main__ - INFO - 📤 点击发送消息按钮: (1686, 900)
2025-08-05 14:32:06,704 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1686, 900)
2025-08-05 14:32:07,205 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:32:07,206 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:32:08,887 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:32:08,888 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:32:08,888 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:32:09,090 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:32:10,392 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:32:10,392 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:32:10,393 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:32:10,394 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:32:10,395 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:32:10,395 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:32:10,395 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:32:10,396 - __main__ - INFO - 🔍 策略2: 使用区域扫描查找发送消息按钮
2025-08-05 14:32:10,396 - __main__ - INFO - 📤 点击发送消息按钮: (1686, 900)
2025-08-05 14:32:11,436 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1686, 900)
2025-08-05 14:32:11,937 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:32:11,937 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:32:13,629 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:32:13,635 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:32:13,638 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:32:13,859 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:32:15,160 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:32:15,161 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:32:15,161 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:32:15,163 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:32:15,163 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:32:15,164 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:32:15,164 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:32:15,167 - __main__ - INFO - 🔍 策略2: 使用区域扫描查找发送消息按钮
2025-08-05 14:32:15,168 - __main__ - INFO - 📤 点击发送消息按钮: (1686, 900)
2025-08-05 14:32:16,219 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1686, 900)
2025-08-05 14:32:16,720 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:32:16,721 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:32:18,403 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:32:18,404 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:32:18,404 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:32:18,611 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:32:19,922 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:32:19,922 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:32:19,923 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:32:19,924 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:32:19,925 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:32:19,925 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:32:19,925 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:32:19,925 - __main__ - INFO - 🔍 策略2: 使用区域扫描查找发送消息按钮
2025-08-05 14:32:19,926 - __main__ - INFO - 📤 点击发送消息按钮: (1686, 900)
2025-08-05 14:32:20,969 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1686, 900)
2025-08-05 14:32:21,470 - __main__ - INFO - 📋 处理第 6/100 个联系人
2025-08-05 14:32:21,471 - __main__ - INFO - 🖱️ 点击联系人位置 6: (1470, 176)
2025-08-05 14:32:22,662 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 14:40:49,327 - __main__ - INFO - ✅ UI Automation 初始化成功
2025-08-05 14:40:49,329 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:40:49,331 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:40:49,331 - __main__ - INFO - 📤 发送消息按钮: 智能UI元素定位（基于UI Automation）
2025-08-05 14:40:49,340 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:40:49,346 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:40:49,356 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:40:49,428 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:40:49,433 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:40:49,439 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:40:49,440 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:40:49,441 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:40:49,972 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:40:50,014 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:40:50,838 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:40:52,095 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:40:53,354 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:40:53,855 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:40:53,855 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:40:55,538 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:40:55,539 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:40:55,539 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:40:55,741 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:40:57,043 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:40:57,044 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:40:57,045 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:40:57,046 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:40:57,047 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:40:57,047 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:40:57,047 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:40:57,159 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:40:57,163 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:40:57,164 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:40:57,165 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:40:58,197 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:40:58,698 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:40:58,700 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:41:00,406 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:41:00,406 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:41:00,407 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:41:00,609 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:41:01,911 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:41:01,911 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:41:01,912 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:41:01,914 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:41:01,914 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:41:01,914 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:41:01,915 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:41:01,989 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:41:01,993 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:41:01,994 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:41:01,995 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:41:03,037 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:41:03,538 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:41:03,539 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:41:05,221 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:41:05,221 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:41:05,222 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:41:05,424 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:41:06,725 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:41:06,726 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:41:06,726 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:41:06,728 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:41:06,728 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:41:06,728 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:41:06,729 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:41:06,802 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:41:06,806 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:41:06,808 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:41:06,809 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:41:07,853 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:41:08,354 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:41:08,355 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:41:10,059 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:41:10,113 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:41:10,329 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:41:10,618 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:41:11,943 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:41:11,943 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:41:11,943 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:41:11,945 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:41:11,946 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:41:11,946 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:41:11,946 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:41:12,018 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:41:12,022 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:41:12,023 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:41:12,024 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:41:13,072 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:41:13,574 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:41:13,574 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:41:15,253 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:41:15,253 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:41:15,254 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:41:15,456 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:41:16,757 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:41:16,757 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:41:16,758 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:41:16,759 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:41:16,760 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:41:16,760 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:41:16,760 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:41:16,820 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:41:16,825 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:41:16,826 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:41:16,826 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:41:17,871 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:41:18,372 - __main__ - INFO - 📋 处理第 6/100 个联系人
2025-08-05 14:41:18,375 - __main__ - INFO - 🖱️ 点击联系人位置 6: (1470, 176)
2025-08-05 14:41:20,054 - __main__ - INFO - ✅ 成功点击位置 6: (1470, 176)
2025-08-05 14:41:20,054 - __main__ - INFO - ✅ 成功点击第 6 个联系人
2025-08-05 14:41:20,055 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:41:20,257 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:41:21,558 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:41:21,559 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:41:21,559 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:41:21,562 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:41:21,563 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:41:21,568 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:41:21,570 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:41:21,833 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:41:21,843 - __main__ - INFO - ✅ 找到发送消息按钮: 位置(1710, 922), 尺寸(24x24)
2025-08-05 14:41:21,844 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:41:21,847 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:41:21,973 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 14:46:41,796 - __main__ - INFO - ✅ UI Automation 初始化成功
2025-08-05 14:46:41,796 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:46:41,796 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:46:41,797 - __main__ - INFO - 📤 发送消息按钮: 智能UI元素定位（基于UI Automation）
2025-08-05 14:46:41,799 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:46:41,801 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 160x28
2025-08-05 14:46:41,802 - __main__ - WARNING - ⚠️ 窗口尺寸过小 (160x28)，尝试恢复窗口...
2025-08-05 14:46:42,338 - __main__ - INFO - 🔄 恢复后窗口尺寸: 700x1000
2025-08-05 14:46:42,338 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:46:42,340 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:46:42,340 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:46:42,342 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:46:42,342 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:46:42,342 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:46:42,843 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:46:42,843 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:46:43,599 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:46:44,856 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:46:46,113 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:46:46,614 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:46:46,614 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:46:48,298 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:46:48,298 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:46:48,299 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:46:48,502 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:46:49,803 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:46:49,803 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:46:49,804 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:46:49,806 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:46:49,806 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:46:49,807 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:46:49,807 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:46:49,916 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:46:49,917 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:46:49,918 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:46:49,918 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:46:49,930 - __main__ - INFO - 🔍 找到 1 个候选按钮，开始选择最佳按钮...
2025-08-05 14:46:49,930 - __main__ - INFO - 📋 候选按钮 4: 位置(1710, 922), 尺寸(24, 24), Name=''
2025-08-05 14:46:49,930 - __main__ - INFO - ✅ 选择最佳发送消息按钮: 按钮4, 位置(1710, 922), 评分26
2025-08-05 14:46:49,931 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:46:49,931 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:46:50,982 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:46:51,482 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:46:51,483 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:46:53,164 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:46:53,165 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:46:53,165 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:46:53,367 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:46:54,668 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:46:54,669 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:46:54,669 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:46:54,671 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:46:54,672 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:46:54,672 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:46:54,672 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:46:54,766 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:46:54,767 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:46:54,768 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:46:54,768 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:46:54,779 - __main__ - INFO - 🔍 找到 1 个候选按钮，开始选择最佳按钮...
2025-08-05 14:46:54,779 - __main__ - INFO - 📋 候选按钮 4: 位置(1710, 922), 尺寸(24, 24), Name=''
2025-08-05 14:46:54,780 - __main__ - INFO - ✅ 选择最佳发送消息按钮: 按钮4, 位置(1710, 922), 评分26
2025-08-05 14:46:54,781 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:46:54,781 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:46:55,830 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:46:56,331 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:46:56,332 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:46:58,014 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:46:58,015 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:46:58,015 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:46:58,218 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:46:59,519 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:46:59,519 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:46:59,520 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:46:59,521 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:46:59,522 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:46:59,522 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:46:59,522 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:46:59,613 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:46:59,614 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:46:59,615 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:46:59,615 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:46:59,626 - __main__ - INFO - 🔍 找到 1 个候选按钮，开始选择最佳按钮...
2025-08-05 14:46:59,626 - __main__ - INFO - 📋 候选按钮 4: 位置(1710, 922), 尺寸(24, 24), Name=''
2025-08-05 14:46:59,627 - __main__ - INFO - ✅ 选择最佳发送消息按钮: 按钮4, 位置(1710, 922), 评分26
2025-08-05 14:46:59,628 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:46:59,628 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:47:00,664 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:47:01,165 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:47:01,165 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:47:02,847 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:47:02,848 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:47:02,849 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:47:03,051 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:47:04,353 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:47:04,353 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:47:04,353 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:47:04,355 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:47:04,356 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:47:04,356 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:47:04,356 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:47:04,449 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:47:04,450 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:47:04,451 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:47:04,451 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:47:04,462 - __main__ - INFO - 🔍 找到 1 个候选按钮，开始选择最佳按钮...
2025-08-05 14:47:04,462 - __main__ - INFO - 📋 候选按钮 4: 位置(1710, 922), 尺寸(24, 24), Name=''
2025-08-05 14:47:04,462 - __main__ - INFO - ✅ 选择最佳发送消息按钮: 按钮4, 位置(1710, 922), 评分26
2025-08-05 14:47:04,463 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:47:04,464 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:47:05,497 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:47:05,998 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:47:05,998 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:47:07,681 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:47:07,681 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:47:07,682 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:47:07,884 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:47:09,185 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:47:09,186 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:47:09,186 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:47:09,188 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:47:09,188 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:47:09,188 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:47:09,189 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:47:09,290 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:47:09,290 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:47:09,292 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:47:09,292 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:47:09,308 - __main__ - INFO - 🔍 找到 1 个候选按钮，开始选择最佳按钮...
2025-08-05 14:47:09,309 - __main__ - INFO - 📋 候选按钮 4: 位置(1710, 922), 尺寸(24, 24), Name=''
2025-08-05 14:47:09,309 - __main__ - INFO - ✅ 选择最佳发送消息按钮: 按钮4, 位置(1710, 922), 评分26
2025-08-05 14:47:09,310 - __main__ - INFO - ✅ UI Automation找到按钮位置: (1710, 922)
2025-08-05 14:47:09,311 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:47:10,347 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:47:10,848 - __main__ - INFO - 📋 处理第 6/100 个联系人
2025-08-05 14:47:10,848 - __main__ - INFO - 🖱️ 点击联系人位置 6: (1470, 176)
2025-08-05 14:47:12,261 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 14:48:01,495 - __main__ - INFO - ✅ UI Automation 初始化成功
2025-08-05 14:48:01,496 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:48:01,496 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:48:01,496 - __main__ - INFO - 📤 发送消息按钮: 智能UI元素定位（基于UI Automation）
2025-08-05 14:48:01,497 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:01,500 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:01,500 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:01,503 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:48:01,504 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:01,506 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:01,508 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:01,510 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:48:02,012 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:48:02,013 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:48:02,771 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:48:04,030 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:48:05,286 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:48:05,787 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:48:05,787 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:48:07,462 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:48:07,462 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:48:07,463 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:48:07,665 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:48:08,966 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:48:08,966 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:48:08,966 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:08,968 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:08,968 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:08,968 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:48:08,969 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:48:09,049 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:48:09,049 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:09,050 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:09,051 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:09,063 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:48:09,063 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:48:09,063 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:48:09,128 - __main__ - INFO - ✅ 替代搜索找到底部元素: (1710, 922)
2025-08-05 14:48:09,130 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:48:10,164 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:48:10,664 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:48:10,665 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:48:12,344 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:48:12,344 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:48:12,345 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:48:12,547 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:48:13,848 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:48:13,849 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:48:13,849 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:13,851 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:13,851 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:13,851 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:48:13,852 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:48:13,929 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:48:13,929 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:13,930 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:13,931 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:13,941 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:48:13,942 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:48:13,944 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:48:13,999 - __main__ - INFO - ✅ 替代搜索找到底部元素: (1710, 922)
2025-08-05 14:48:14,000 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:48:15,043 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:48:15,544 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:48:15,544 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:48:17,227 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:48:17,227 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:48:17,228 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:48:17,430 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:48:18,731 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:48:18,732 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:48:18,732 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:18,734 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:18,735 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:18,735 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:48:18,736 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:48:18,811 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:48:18,832 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:18,833 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:18,851 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:18,884 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:48:18,898 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:48:18,913 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:48:19,042 - __main__ - INFO - ✅ 替代搜索找到底部元素: (1710, 922)
2025-08-05 14:48:19,058 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:48:20,143 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:48:20,644 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:48:20,644 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:48:22,328 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:48:22,328 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:48:22,328 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:48:22,530 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:48:23,833 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:48:23,836 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:48:23,837 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:23,842 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:23,845 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:23,850 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:48:23,851 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:48:23,899 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:48:23,907 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:23,922 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:23,944 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:23,969 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:48:23,985 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:48:24,011 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:48:24,106 - __main__ - INFO - ✅ 替代搜索找到底部元素: (1710, 922)
2025-08-05 14:48:24,121 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:48:25,160 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1710, 922)
2025-08-05 14:48:25,661 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:48:25,661 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:48:27,343 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:48:27,344 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:48:27,344 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:48:27,546 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:48:28,847 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:48:28,848 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:48:28,849 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:28,851 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:28,851 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:28,852 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:48:28,853 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:48:29,316 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:48:29,316 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:48:29,319 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:48:29,320 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:48:29,379 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:48:29,397 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:48:29,398 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:48:29,514 - __main__ - INFO - ✅ 替代搜索找到底部元素: (1710, 922)
2025-08-05 14:48:29,515 - __main__ - INFO - 📤 点击发送消息按钮: (1710, 922)
2025-08-05 14:48:29,752 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 14:49:20,425 - __main__ - INFO - ✅ UI Automation 初始化成功
2025-08-05 14:49:20,426 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:49:20,426 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:49:20,426 - __main__ - INFO - 📤 发送消息按钮: 智能UI元素定位（基于UI Automation）
2025-08-05 14:49:20,428 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:20,431 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:20,433 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:20,441 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:49:20,441 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:20,444 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:20,458 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:20,459 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:49:20,969 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:49:20,971 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:49:21,745 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:49:23,083 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:49:24,341 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:49:24,842 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:49:24,842 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:49:26,574 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:49:26,575 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:49:26,575 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:49:26,777 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:49:28,078 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:49:28,079 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:49:28,079 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:28,082 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:28,083 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:28,083 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:49:28,087 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:49:28,210 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:49:28,211 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:28,212 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:28,213 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:28,223 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:49:28,224 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:49:28,224 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:49:28,300 - __main__ - INFO - 🔍 策略3: 使用固定位置备选方案
2025-08-05 14:49:28,300 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 924)
2025-08-05 14:49:29,339 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1562, 924)
2025-08-05 14:49:29,840 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:49:29,841 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:49:31,523 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:49:31,523 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:49:31,523 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:49:31,726 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:49:33,027 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:49:33,027 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:49:33,028 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:33,029 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:33,030 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:33,030 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:49:33,030 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:49:33,093 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:49:33,093 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:33,094 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:33,095 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:33,108 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:49:33,108 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:49:33,109 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:49:33,161 - __main__ - INFO - 🔍 策略3: 使用固定位置备选方案
2025-08-05 14:49:33,162 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 924)
2025-08-05 14:49:34,205 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1562, 924)
2025-08-05 14:49:34,706 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:49:34,707 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:49:36,389 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:49:36,390 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:49:36,391 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:49:36,594 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:49:37,895 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:49:37,895 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:49:37,896 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:37,898 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:37,898 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:37,899 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:49:37,899 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:49:37,971 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:49:37,972 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:37,973 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:37,974 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:37,983 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:49:37,983 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:49:37,987 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:49:38,063 - __main__ - INFO - 🔍 策略3: 使用固定位置备选方案
2025-08-05 14:49:38,064 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 924)
2025-08-05 14:49:39,105 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1562, 924)
2025-08-05 14:49:39,606 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:49:39,606 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:49:41,276 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:49:41,276 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:49:41,277 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:49:41,479 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:49:42,780 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:49:42,781 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:49:42,781 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:42,783 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:42,786 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:42,786 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:49:42,786 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:49:42,863 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:49:42,863 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:42,865 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:42,867 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:42,880 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:49:42,880 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:49:42,881 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:49:42,947 - __main__ - INFO - 🔍 策略3: 使用固定位置备选方案
2025-08-05 14:49:42,947 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 924)
2025-08-05 14:49:43,989 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1562, 924)
2025-08-05 14:49:44,490 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:49:44,490 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:49:46,171 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:49:46,172 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:49:46,173 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:49:46,377 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:49:47,678 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:49:47,679 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:49:47,679 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:47,680 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:47,681 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:47,681 - __main__ - INFO - 🔍 策略1: 使用UI Automation查找发送消息按钮
2025-08-05 14:49:47,682 - __main__ - INFO - 🔍 使用UI Automation查找发送消息按钮...
2025-08-05 14:49:47,753 - __main__ - INFO - 🔍 找到 9 个符合条件的按钮，开始验证...
2025-08-05 14:49:47,754 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:49:47,756 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:49:47,757 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:49:47,772 - __main__ - INFO - ⚠️ UI Automation未找到按钮，尝试备选方案
2025-08-05 14:49:47,774 - __main__ - INFO - 🔍 策略2: 使用替代UI搜索查找发送消息按钮
2025-08-05 14:49:47,774 - __main__ - INFO - 🔍 使用替代UI搜索策略...
2025-08-05 14:49:47,829 - __main__ - INFO - 🔍 策略3: 使用固定位置备选方案
2025-08-05 14:49:47,830 - __main__ - INFO - 📤 点击发送消息按钮: (1562, 924)
2025-08-05 14:49:48,008 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 14:58:36,745 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 14:58:36,746 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 14:58:36,746 - __main__ - INFO - 📤 发送消息按钮: 图像识别定位技术
2025-08-05 14:58:36,747 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:58:36,750 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 160x28
2025-08-05 14:58:36,752 - __main__ - WARNING - ⚠️ 窗口尺寸过小 (160x28)，尝试恢复窗口...
2025-08-05 14:58:37,298 - __main__ - INFO - 🔄 恢复后窗口尺寸: 700x1000
2025-08-05 14:58:37,319 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:58:37,390 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 14:58:37,391 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:58:37,398 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:58:37,403 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:58:37,407 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 14:58:37,911 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 14:58:37,913 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 14:58:38,673 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:58:39,931 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:58:41,189 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 14:58:41,690 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 14:58:41,691 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 14:58:43,372 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 14:58:43,372 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 14:58:43,372 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:58:43,575 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:58:44,875 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:58:44,876 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:58:44,876 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:58:44,878 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:58:44,879 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:58:44,879 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:58:44,879 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:58:44,977 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:58:44,978 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:58:44,978 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:58:44,978 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:58:44,979 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:58:46,105 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:58:46,608 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 14:58:46,609 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 14:58:48,288 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 14:58:48,289 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 14:58:48,289 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:58:48,492 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:58:49,793 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:58:49,794 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:58:49,794 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:58:49,796 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:58:49,796 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:58:49,797 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:58:49,797 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:58:49,881 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:58:49,882 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:58:49,882 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:58:49,886 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:58:49,887 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:58:50,920 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:58:51,421 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 14:58:51,422 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
2025-08-05 14:58:53,092 - __main__ - INFO - ✅ 成功点击位置 3: (1470, 176)
2025-08-05 14:58:53,093 - __main__ - INFO - ✅ 成功点击第 3 个联系人
2025-08-05 14:58:53,093 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:58:53,296 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:58:54,597 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:58:54,597 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:58:54,597 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:58:54,601 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:58:54,601 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:58:54,602 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:58:54,602 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:58:54,702 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:58:54,704 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:58:54,704 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:58:54,705 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:58:54,705 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:58:55,755 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:58:56,256 - __main__ - INFO - 📋 处理第 4/100 个联系人
2025-08-05 14:58:56,256 - __main__ - INFO - 🖱️ 点击联系人位置 4: (1470, 176)
2025-08-05 14:58:57,939 - __main__ - INFO - ✅ 成功点击位置 4: (1470, 176)
2025-08-05 14:58:57,939 - __main__ - INFO - ✅ 成功点击第 4 个联系人
2025-08-05 14:58:57,940 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:58:58,143 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:58:59,444 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:58:59,445 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:58:59,446 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:58:59,447 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:58:59,448 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:58:59,448 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:58:59,453 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:58:59,579 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:58:59,580 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:58:59,581 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:58:59,581 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:58:59,581 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:59:00,620 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:59:01,122 - __main__ - INFO - 📋 处理第 5/100 个联系人
2025-08-05 14:59:01,122 - __main__ - INFO - 🖱️ 点击联系人位置 5: (1470, 176)
2025-08-05 14:59:02,838 - __main__ - INFO - ✅ 成功点击位置 5: (1470, 176)
2025-08-05 14:59:02,868 - __main__ - INFO - ✅ 成功点击第 5 个联系人
2025-08-05 14:59:02,906 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:59:03,151 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:59:04,453 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:59:04,454 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:59:04,457 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:59:04,462 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:59:04,468 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:59:04,472 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:59:04,474 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:59:04,613 - __main__ - INFO - 🔍 检测到 1 个按钮候选区域
2025-08-05 14:59:04,613 - __main__ - INFO - 🔍 验证 1 个按钮候选...
2025-08-05 14:59:04,614 - __main__ - INFO - ✅ 选择最佳按钮: 全局位置(1474, 808), 评分18
2025-08-05 14:59:04,643 - __main__ - INFO - 💾 检测结果已保存: debug_images\detection_result_1754405944.png
2025-08-05 14:59:04,644 - __main__ - INFO - ✅ 图像识别找到按钮位置: (1474, 808)
2025-08-05 14:59:04,645 - __main__ - INFO - 📤 点击发送消息按钮: (1474, 808)
2025-08-05 14:59:05,737 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1474, 808)
2025-08-05 14:59:06,238 - __main__ - INFO - 📋 处理第 6/100 个联系人
2025-08-05 14:59:06,239 - __main__ - INFO - 🖱️ 点击联系人位置 6: (1470, 176)
2025-08-05 14:59:07,921 - __main__ - INFO - ✅ 成功点击位置 6: (1470, 176)
2025-08-05 14:59:07,921 - __main__ - INFO - ✅ 成功点击第 6 个联系人
2025-08-05 14:59:07,922 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:59:08,124 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:59:09,425 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:59:09,426 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:59:09,426 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:59:09,428 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:59:09,428 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:59:09,428 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:59:09,429 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:59:09,547 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:59:09,548 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:59:09,552 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:59:09,553 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:59:09,554 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:59:10,605 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:59:11,106 - __main__ - INFO - 📋 处理第 7/100 个联系人
2025-08-05 14:59:11,107 - __main__ - INFO - 🖱️ 点击联系人位置 7: (1470, 176)
2025-08-05 14:59:12,787 - __main__ - INFO - ✅ 成功点击位置 7: (1470, 176)
2025-08-05 14:59:12,788 - __main__ - INFO - ✅ 成功点击第 7 个联系人
2025-08-05 14:59:12,788 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:59:12,990 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:59:14,291 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:59:14,291 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:59:14,291 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:59:14,293 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:59:14,293 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:59:14,294 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:59:14,294 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:59:14,423 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:59:14,425 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:59:14,425 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:59:14,426 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:59:14,426 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:59:15,459 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:59:15,988 - __main__ - INFO - 📋 处理第 8/100 个联系人
2025-08-05 14:59:16,018 - __main__ - INFO - 🖱️ 点击联系人位置 8: (1470, 176)
2025-08-05 14:59:17,703 - __main__ - INFO - ✅ 成功点击位置 8: (1470, 176)
2025-08-05 14:59:17,704 - __main__ - INFO - ✅ 成功点击第 8 个联系人
2025-08-05 14:59:17,705 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:59:17,908 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:59:19,208 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:59:19,209 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:59:19,209 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:59:19,210 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:59:19,211 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:59:19,211 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:59:19,211 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:59:19,322 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:59:19,322 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:59:19,323 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:59:19,323 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:59:19,324 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:59:20,370 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:59:20,872 - __main__ - INFO - 📋 处理第 9/100 个联系人
2025-08-05 14:59:20,872 - __main__ - INFO - 🖱️ 点击联系人位置 9: (1470, 176)
2025-08-05 14:59:22,571 - __main__ - INFO - ✅ 成功点击位置 9: (1470, 176)
2025-08-05 14:59:22,572 - __main__ - INFO - ✅ 成功点击第 9 个联系人
2025-08-05 14:59:22,572 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:59:22,774 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:59:24,075 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:59:24,076 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:59:24,076 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:59:24,078 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:59:24,078 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:59:24,079 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:59:24,079 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:59:24,191 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:59:24,192 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:59:24,193 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:59:24,193 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:59:24,193 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:59:25,237 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:59:25,737 - __main__ - INFO - 📋 处理第 10/100 个联系人
2025-08-05 14:59:25,738 - __main__ - INFO - 🖱️ 点击联系人位置 10: (1470, 176)
2025-08-05 14:59:27,419 - __main__ - INFO - ✅ 成功点击位置 10: (1470, 176)
2025-08-05 14:59:27,420 - __main__ - INFO - ✅ 成功点击第 10 个联系人
2025-08-05 14:59:27,420 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:59:27,622 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:59:28,924 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:59:28,924 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:59:28,925 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:59:28,926 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:59:28,927 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:59:28,927 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:59:28,927 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:59:29,024 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:59:29,025 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:59:29,025 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:59:29,026 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:59:29,026 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:59:30,061 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:59:30,567 - __main__ - INFO - 📋 处理第 11/100 个联系人
2025-08-05 14:59:30,567 - __main__ - INFO - 🖱️ 点击联系人位置 11: (1470, 176)
2025-08-05 14:59:32,321 - __main__ - INFO - ✅ 成功点击位置 11: (1470, 176)
2025-08-05 14:59:32,321 - __main__ - INFO - ✅ 成功点击第 11 个联系人
2025-08-05 14:59:32,322 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:59:32,524 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:59:33,826 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:59:33,827 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:59:33,827 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:59:33,829 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:59:33,829 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:59:33,829 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:59:33,830 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:59:33,939 - __main__ - INFO - 🔍 检测到 1 个按钮候选区域
2025-08-05 14:59:33,940 - __main__ - INFO - 🔍 验证 1 个按钮候选...
2025-08-05 14:59:33,940 - __main__ - INFO - ✅ 选择最佳按钮: 全局位置(1477, 814), 评分18
2025-08-05 14:59:33,961 - __main__ - INFO - 💾 检测结果已保存: debug_images\detection_result_1754405973.png
2025-08-05 14:59:33,963 - __main__ - INFO - ✅ 图像识别找到按钮位置: (1477, 814)
2025-08-05 14:59:33,963 - __main__ - INFO - 📤 点击发送消息按钮: (1477, 814)
2025-08-05 14:59:35,008 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1477, 814)
2025-08-05 14:59:35,508 - __main__ - INFO - 📋 处理第 12/100 个联系人
2025-08-05 14:59:35,509 - __main__ - INFO - 🖱️ 点击联系人位置 12: (1470, 176)
2025-08-05 14:59:37,185 - __main__ - INFO - ✅ 成功点击位置 12: (1470, 176)
2025-08-05 14:59:37,186 - __main__ - INFO - ✅ 成功点击第 12 个联系人
2025-08-05 14:59:37,186 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:59:37,388 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:59:38,689 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:59:38,690 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:59:38,690 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:59:38,692 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:59:38,692 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:59:38,692 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:59:38,693 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:59:38,794 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:59:38,794 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:59:38,795 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:59:38,795 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:59:38,796 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:59:39,835 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:59:40,336 - __main__ - INFO - 📋 处理第 13/100 个联系人
2025-08-05 14:59:40,337 - __main__ - INFO - 🖱️ 点击联系人位置 13: (1470, 176)
2025-08-05 14:59:42,066 - __main__ - INFO - ✅ 成功点击位置 13: (1470, 176)
2025-08-05 14:59:42,090 - __main__ - INFO - ✅ 成功点击第 13 个联系人
2025-08-05 14:59:42,127 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 14:59:42,371 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 14:59:43,672 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 14:59:43,672 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 14:59:43,672 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 14:59:43,674 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 14:59:43,675 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 14:59:43,675 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 14:59:43,676 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 14:59:43,772 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 14:59:43,773 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 14:59:43,773 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 14:59:43,774 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 14:59:43,774 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 14:59:44,905 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 14:59:45,406 - __main__ - INFO - 📋 处理第 14/100 个联系人
2025-08-05 14:59:45,407 - __main__ - INFO - 🖱️ 点击联系人位置 14: (1470, 176)
2025-08-05 14:59:45,941 - __main__ - INFO - ⚠️ 用户中断操作
2025-08-05 15:05:18,731 - __main__ - INFO - ✅ 基于坐标的联系人点击器初始化完成
2025-08-05 15:05:18,732 - __main__ - INFO - 📐 滚动配置: 步数=55, 联系人高度=48px
2025-08-05 15:05:18,732 - __main__ - INFO - 📤 发送消息按钮: 图像识别定位技术
2025-08-05 15:05:18,734 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 15:05:18,737 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 15:05:18,746 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 15:05:18,747 - __main__ - WARNING - ⚠️ 窗口枚举异常: (183, 'EnumWindows', '当文件已存在时，无法创建该文件。')
2025-08-05 15:05:18,750 - __main__ - INFO - ✅ 通过标题找到窗口: '通讯录管理' 句柄: 3539538
2025-08-05 15:05:18,758 - __main__ - INFO - 🚀 开始处理联系人（点击一个滚动一次）...
2025-08-05 15:05:18,758 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 15:05:18,760 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 15:05:18,768 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 15:05:18,769 - __main__ - INFO - 🔄 激活窗口 3539538...
2025-08-05 15:05:19,274 - __main__ - INFO - ✅ 窗口激活成功 (方法1)
2025-08-05 15:05:19,276 - __main__ - INFO - 📜 滚动到列表顶部...
2025-08-05 15:05:20,033 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 15:05:21,290 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 15:05:22,548 - __main__ - INFO - 📜 向上滚动 3 步
2025-08-05 15:05:23,049 - __main__ - INFO - 📋 处理第 1/100 个联系人
2025-08-05 15:05:23,050 - __main__ - INFO - 🖱️ 点击联系人位置 1: (1470, 176)
2025-08-05 15:05:24,781 - __main__ - INFO - ✅ 成功点击位置 1: (1470, 176)
2025-08-05 15:05:24,781 - __main__ - INFO - ✅ 成功点击第 1 个联系人
2025-08-05 15:05:24,782 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 15:05:24,984 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 15:05:26,285 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 15:05:26,286 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 15:05:26,287 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 15:05:26,289 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 15:05:26,289 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 15:05:26,290 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 15:05:26,290 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 15:05:26,388 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 15:05:26,388 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 15:05:26,389 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 15:05:26,389 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 15:05:26,389 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 15:05:27,429 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 15:05:27,930 - __main__ - INFO - 📋 处理第 2/100 个联系人
2025-08-05 15:05:27,931 - __main__ - INFO - 🖱️ 点击联系人位置 2: (1470, 176)
2025-08-05 15:05:29,613 - __main__ - INFO - ✅ 成功点击位置 2: (1470, 176)
2025-08-05 15:05:29,613 - __main__ - INFO - ✅ 成功点击第 2 个联系人
2025-08-05 15:05:29,613 - __main__ - INFO - 📜 向下滚动显示下一个联系人...
2025-08-05 15:05:29,815 - __main__ - INFO - 📜 向下滚动 55 步
2025-08-05 15:05:31,118 - __main__ - INFO - 📤 点击发送消息按钮...
2025-08-05 15:05:31,118 - __main__ - INFO - 📤 智能定位发送消息按钮...
2025-08-05 15:05:31,118 - __main__ - INFO - 🔍 查找通讯录管理窗口...
2025-08-05 15:05:31,120 - __main__ - INFO - 🔍 找到通讯录管理窗口: '通讯录管理' 句柄: 3539538 尺寸: 700x1000
2025-08-05 15:05:31,120 - __main__ - INFO - ✅ 接受通讯录管理窗口: '通讯录管理' 句柄: 3539538
2025-08-05 15:05:31,121 - __main__ - INFO - 🔍 策略1: 使用图像识别查找发送消息按钮
2025-08-05 15:05:31,121 - __main__ - INFO - 🔍 使用图像识别查找发送消息按钮...
2025-08-05 15:05:31,223 - __main__ - INFO - 🔍 检测到 0 个按钮候选区域
2025-08-05 15:05:31,223 - __main__ - WARNING - ⚠️ 未检测到按钮候选区域
2025-08-05 15:05:31,225 - __main__ - INFO - ⚠️ 图像识别未找到按钮，尝试备选方案
2025-08-05 15:05:31,226 - __main__ - INFO - 🔍 策略2: 使用备选固定位置方案
2025-08-05 15:05:31,228 - __main__ - INFO - 📤 点击发送消息按钮: (1745, 950)
2025-08-05 15:05:32,368 - __main__ - INFO - ✅ 成功点击发送消息按钮: (1745, 950)
2025-08-05 15:05:32,888 - __main__ - INFO - 📋 处理第 3/100 个联系人
2025-08-05 15:05:32,890 - __main__ - INFO - 🖱️ 点击联系人位置 3: (1470, 176)
