#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像识别功能测试脚本
用于验证OpenCV和PIL库是否正确安装，以及图像识别功能是否正常工作
"""

import sys
import os

def test_imports():
    """测试必要的库导入"""
    print("🔍 测试库导入...")
    
    try:
        import cv2
        print(f"✅ OpenCV 导入成功，版本: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy 导入成功，版本: {np.__version__}")
    except ImportError as e:
        print(f"❌ NumPy 导入失败: {e}")
        return False
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        print(f"✅ PIL 导入成功，版本: {Image.__version__}")
    except ImportError as e:
        print(f"❌ PIL 导入失败: {e}")
        return False
    
    try:
        import pyautogui
        print(f"✅ PyAutoGUI 导入成功，版本: {pyautogui.__version__}")
    except ImportError as e:
        print(f"❌ PyAutoGUI 导入失败: {e}")
        return False
    
    return True

def test_screenshot():
    """测试截图功能"""
    print("\n📸 测试截图功能...")
    
    try:
        import pyautogui
        import cv2
        import numpy as np
        
        # 截取屏幕
        screenshot = pyautogui.screenshot()
        print(f"✅ 截图成功，尺寸: {screenshot.size}")
        
        # 转换为OpenCV格式
        screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        print(f"✅ 转换为OpenCV格式成功，形状: {screenshot_cv.shape}")
        
        # 保存测试截图
        test_dir = "test_images"
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
        
        test_path = os.path.join(test_dir, "test_screenshot.png")
        cv2.imwrite(test_path, screenshot_cv)
        print(f"✅ 测试截图已保存: {test_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 截图测试失败: {e}")
        return False

def test_contour_detection():
    """测试轮廓检测功能"""
    print("\n🔍 测试轮廓检测功能...")
    
    try:
        import cv2
        import numpy as np
        
        # 创建测试图像（包含一些矩形）
        test_image = np.zeros((200, 300, 3), dtype=np.uint8)
        
        # 绘制一些测试矩形
        cv2.rectangle(test_image, (50, 50), (100, 100), (255, 255, 255), -1)
        cv2.rectangle(test_image, (150, 80), (200, 130), (255, 255, 255), -1)
        cv2.rectangle(test_image, (220, 120), (270, 170), (255, 255, 255), -1)
        
        # 转换为灰度图
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        
        # 查找轮廓
        contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        print(f"✅ 检测到 {len(contours)} 个轮廓")
        
        # 分析轮廓
        valid_contours = 0
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            area = cv2.contourArea(contour)
            
            if 20 <= w <= 60 and 20 <= h <= 60:  # 符合按钮尺寸
                valid_contours += 1
                print(f"  - 有效轮廓: 位置({x}, {y}), 尺寸({w}x{h}), 面积: {area}")
        
        print(f"✅ 找到 {valid_contours} 个符合条件的轮廓")
        
        # 保存测试结果
        test_dir = "test_images"
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
        
        # 绘制轮廓
        result_image = test_image.copy()
        cv2.drawContours(result_image, contours, -1, (0, 255, 0), 2)
        
        test_path = os.path.join(test_dir, "test_contours.png")
        cv2.imwrite(test_path, result_image)
        print(f"✅ 轮廓检测结果已保存: {test_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 轮廓检测测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎉 图像识别功能测试")
    print("=" * 50)
    
    # 测试库导入
    if not test_imports():
        print("\n❌ 库导入测试失败，请安装缺失的库:")
        print("pip install opencv-python pillow numpy pyautogui")
        return
    
    # 测试截图功能
    if not test_screenshot():
        print("\n❌ 截图功能测试失败")
        return
    
    # 测试轮廓检测
    if not test_contour_detection():
        print("\n❌ 轮廓检测测试失败")
        return
    
    print("\n✅ 所有测试通过！图像识别功能准备就绪")
    print("\n📋 测试结果:")
    print("  - 库导入: ✅")
    print("  - 截图功能: ✅")
    print("  - 轮廓检测: ✅")
    print("\n🚀 可以开始使用图像识别功能了！")

if __name__ == "__main__":
    main()
