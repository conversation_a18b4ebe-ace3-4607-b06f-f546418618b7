#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于坐标的联系人点击脚本
功能：由于联系人是渲染元素，使用坐标网格点击方式来操作联系人列表
"""

import win32gui
import pyautogui
import time
import logging
from typing import List, Tuple, Optional

# UI Automation 相关导入
try:
    import comtypes.client
    from comtypes.gen import UIAutomationClient
    UI_AUTOMATION_AVAILABLE = True
except ImportError:
    print("⚠️ UI Automation 模块未安装，将使用备选方案")
    UI_AUTOMATION_AVAILABLE = False

class ContactsCoordinateClicker:
    """基于坐标的联系人点击器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 基于UI树信息的联系人列表区
        self.contact_area = {
            'x': 1410,      # 列表左边界
            'y': 76,        # 列表上边界
            'width': 520,   # 列表宽度
            'height': 924   # 列表高度
        }
        
        # 联系人项目配置
        self.contact_item_height = 48  # 每个联系人项目的高度
        self.click_offset_x = 60       # 点击位置相对于左边界的偏移（昵称列位置，基于UI树：1460-1400=60）

        # 滚动步数计算：基于测试结果50-60步对应一个联系人高度(48px)
        # 使用55步作为最佳平衡点，确保精确滚动到下一个联系人
        self.scroll_step = 55         # 单步滚动步数（精确计算，确保滚动一个联系人高度）

        # 固定点击位置配置（单点击模式）
        self.fixed_click_y = self.contact_area['y'] + 200  # 固定Y坐标：列表顶部+200px
        self.single_contact_scroll = 2  # 单个联系人对应的滚动距离

        # 发送消息按钮配置（动态计算，基于联系人位置）
        # 不再使用固定坐标，改为基于联系人位置的相对偏移计算
        
        # 点击配置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.2
        
        # 初始化UI Automation
        self.ui_automation = None
        self.ui_automation_available = UI_AUTOMATION_AVAILABLE

        if self.ui_automation_available:
            try:
                # 初始化COM库
                comtypes.CoInitialize()
                # 创建UI Automation对象
                self.ui_automation = comtypes.client.CreateObject("{ff48dba4-60ef-4201-aa87-54103eef594e}", interface=UIAutomationClient.IUIAutomation)
                self.logger.info("✅ UI Automation 初始化成功")
            except Exception as e:
                self.logger.warning(f"⚠️ UI Automation 初始化失败: {e}")
                self.ui_automation = None
                self.ui_automation_available = False

        self.logger.info("✅ 基于坐标的联系人点击器初始化完成")
        self.logger.info(f"📐 滚动配置: 步数={self.scroll_step}, 联系人高度={self.contact_item_height}px")
        self.logger.info("📤 发送消息按钮: 智能UI元素定位（基于UI Automation）")

    def adjust_scroll_step(self, new_step: int):
        """动态调整滚动步数"""
        old_step = self.scroll_step
        self.scroll_step = new_step
        self.logger.info(f"🔧 滚动步数调整: {old_step} → {new_step}")
        return old_step

    def find_send_message_button_by_ui_automation(self, hwnd: int) -> Optional[Tuple[int, int]]:
        """使用UI Automation真正查找发送消息按钮"""
        try:
            if not self.ui_automation_available or not self.ui_automation:
                self.logger.debug("⚠️ UI Automation 不可用，跳过此策略")
                return None

            self.logger.info("🔍 使用UI Automation查找发送消息按钮...")

            # 从窗口句柄获取根UI元素
            try:
                root_element = self.ui_automation.ElementFromHandle(hwnd)
                if not root_element:
                    self.logger.debug("⚠️ 无法从窗口句柄获取根UI元素")
                    return None

                self.logger.debug("✅ 成功获取根UI元素")
            except Exception as e:
                self.logger.debug(f"⚠️ 获取根UI元素失败: {e}")
                return None

            # 创建查找条件：基于UI树信息的精确匹配
            try:
                # 条件1：控件类型为按钮
                button_condition = self.ui_automation.CreatePropertyCondition(
                    UIAutomationClient.UIA_ControlTypePropertyId,
                    UIAutomationClient.UIA_ButtonControlTypeId
                )

                # 条件2：类名为 mmui::XImage
                class_condition = self.ui_automation.CreatePropertyCondition(
                    UIAutomationClient.UIA_ClassNamePropertyId,
                    "mmui::XImage"
                )

                # 组合条件：必须同时满足两个条件
                combined_condition = self.ui_automation.CreateAndCondition(
                    button_condition, class_condition
                )

                self.logger.debug("✅ 成功创建查找条件")
            except Exception as e:
                self.logger.debug(f"⚠️ 创建查找条件失败: {e}")
                return None

            # 在整个窗口中查找符合条件的按钮
            try:
                buttons = root_element.FindAll(
                    UIAutomationClient.TreeScope_Descendants,
                    combined_condition
                )

                if not buttons or buttons.Length == 0:
                    self.logger.debug("⚠️ 未找到符合条件的按钮元素")
                    return None

                self.logger.info(f"🔍 找到 {buttons.Length} 个符合条件的按钮，开始验证...")

            except Exception as e:
                self.logger.debug(f"⚠️ 查找按钮元素失败: {e}")
                return None

            # 验证每个找到的按钮
            return self._validate_and_select_send_button(buttons)

        except Exception as e:
            self.logger.error(f"❌ UI Automation 查找按钮异常: {e}")
            return None

    def _validate_and_select_send_button(self, buttons) -> Optional[Tuple[int, int]]:
        """验证并选择正确的发送消息按钮（增强版）"""
        try:
            # 获取窗口信息用于相对位置计算
            hwnd = self.find_contacts_management_window()
            window_rect = win32gui.GetWindowRect(hwnd) if hwnd else None

            candidate_buttons = []  # 存储所有候选按钮信息

            for i in range(buttons.Length):
                button = buttons.GetElement(i)

                try:
                    # 获取按钮的位置信息
                    rect = button.CurrentBoundingRectangle
                    button_x = rect.left + (rect.right - rect.left) // 2
                    button_y = rect.top + (rect.bottom - rect.top) // 2
                    button_width = rect.right - rect.left
                    button_height = rect.bottom - rect.top

                    # 获取按钮Name属性
                    button_name = ""
                    try:
                        button_name = button.CurrentName or ""
                    except:
                        pass

                    self.logger.debug(f"🔍 验证按钮 {i+1}: 位置({button_x}, {button_y}), 尺寸({button_width}x{button_height}), Name='{button_name}'")

                    # 验证1：按钮必须在界面底部区域（Y坐标 > 800，更严格的底部限制）
                    if button_y < 800:
                        self.logger.debug(f"⚠️ 按钮 {i+1} 不在底部区域(Y<800)，跳过")
                        continue

                    # 验证2：排除右侧区域的按钮（X坐标不能过大）
                    if window_rect:
                        window_width = window_rect[2] - window_rect[0]
                        # 更严格地排除窗口右侧区域的按钮
                        # 发送消息按钮应该在窗口中央或左侧，不应该在最右侧
                        max_x = window_rect[0] + window_width * 2 // 3  # 排除右侧1/3区域
                        if button_x > max_x:
                            self.logger.debug(f"⚠️ 按钮 {i+1} 在右侧区域(X>{max_x})，可能是修改权限按钮，跳过")
                            continue

                        # 额外验证：如果X坐标超过1650，很可能是右侧功能按钮
                        if button_x > 1650:
                            self.logger.debug(f"⚠️ 按钮 {i+1} X坐标过大({button_x}>1650)，可能是功能按钮，跳过")
                            continue

                    # 验证3：按钮尺寸应该合理（基于UI树信息：24x24）
                    if button_width < 20 or button_width > 50 or button_height < 20 or button_height > 50:
                        self.logger.debug(f"⚠️ 按钮 {i+1} 尺寸不合理，跳过")
                        continue

                    # 验证4：检查按钮是否支持Invoke模式
                    try:
                        invoke_pattern = button.GetCurrentPattern(UIAutomationClient.UIA_InvokePatternId)
                        if not invoke_pattern:
                            self.logger.debug(f"⚠️ 按钮 {i+1} 不支持Invoke模式，跳过")
                            continue
                    except:
                        self.logger.debug(f"⚠️ 按钮 {i+1} Invoke模式检查失败，跳过")
                        continue

                    # 验证5：检查按钮是否可见和可用
                    try:
                        if not button.CurrentIsEnabled or button.CurrentIsOffscreen:
                            self.logger.debug(f"⚠️ 按钮 {i+1} 不可用或不可见，跳过")
                            continue
                    except:
                        self.logger.debug(f"⚠️ 按钮 {i+1} 状态检查失败，跳过")
                        continue

                    # 验证6：检查LocalizedControlType
                    try:
                        control_type = button.CurrentLocalizedControlType
                        if control_type and "按钮" not in control_type:
                            self.logger.debug(f"⚠️ 按钮 {i+1} LocalizedControlType不匹配: {control_type}")
                            continue
                    except:
                        pass  # LocalizedControlType检查失败不影响结果

                    # 验证7：排除明显的非发送消息按钮
                    if button_name and any(keyword in button_name for keyword in ["权限", "设置", "配置", "管理"]):
                        self.logger.debug(f"⚠️ 按钮 {i+1} Name包含非发送消息关键词: '{button_name}'，跳过")
                        continue

                    # 通过所有验证，添加到候选列表
                    candidate_buttons.append({
                        'index': i+1,
                        'position': (button_x, button_y),
                        'size': (button_width, button_height),
                        'name': button_name
                    })

                except Exception as e:
                    self.logger.debug(f"⚠️ 验证按钮 {i+1} 时出错: {e}")
                    continue

            # 从候选按钮中选择最合适的发送消息按钮
            return self._select_best_send_button(candidate_buttons, window_rect)

        except Exception as e:
            self.logger.debug(f"⚠️ 按钮验证过程异常: {e}")
            return None

    def _select_best_send_button(self, candidate_buttons, window_rect) -> Optional[Tuple[int, int]]:
        """从候选按钮中选择最合适的发送消息按钮"""
        try:
            if not candidate_buttons:
                self.logger.debug("⚠️ 没有候选按钮")
                return None

            self.logger.info(f"🔍 找到 {len(candidate_buttons)} 个候选按钮，开始选择最佳按钮...")

            # 显示所有候选按钮信息
            for btn in candidate_buttons:
                self.logger.info(f"📋 候选按钮 {btn['index']}: 位置{btn['position']}, 尺寸{btn['size']}, Name='{btn['name']}'")

            # 选择策略：优先选择位置最合理的按钮
            best_button = None
            best_score = -1

            for btn in candidate_buttons:
                x, y = btn['position']
                score = 0

                # 评分标准1：Y坐标越接近底部越好（但不能太底部）
                if 850 <= y <= 950:
                    score += 10  # 理想的底部位置
                elif 800 <= y < 850:
                    score += 5   # 可接受的位置

                # 评分标准2：X坐标应该在窗口中央或左侧区域
                if window_rect:
                    window_width = window_rect[2] - window_rect[0]
                    window_center_x = window_rect[0] + window_width // 2

                    # 优先选择窗口中央附近的按钮
                    if abs(x - window_center_x) < window_width // 4:
                        score += 8  # 中央区域
                    elif x < window_center_x:
                        score += 5  # 左侧区域

                # 评分标准3：按钮Name为空或包含消息相关关键词
                if not btn['name']:
                    score += 3  # 空Name通常是图标按钮
                elif any(keyword in btn['name'] for keyword in ["消息", "发送", "message", "send"]):
                    score += 8  # 包含消息相关关键词

                # 评分标准4：尺寸接近24x24的按钮优先
                width, height = btn['size']
                if 22 <= width <= 26 and 22 <= height <= 26:
                    score += 5  # 尺寸完全匹配UI树信息

                self.logger.debug(f"🎯 按钮 {btn['index']} 评分: {score}")

                if score > best_score:
                    best_score = score
                    best_button = btn

            if best_button:
                self.logger.info(f"✅ 选择最佳发送消息按钮: 按钮{best_button['index']}, 位置{best_button['position']}, 评分{best_score}")
                return best_button['position']
            else:
                self.logger.warning("⚠️ 无法确定最佳发送消息按钮")
                return None

        except Exception as e:
            self.logger.debug(f"⚠️ 选择最佳按钮异常: {e}")
            return None

    def find_send_message_button_by_alternative_ui_search(self, hwnd: int) -> Optional[Tuple[int, int]]:
        """使用替代UI搜索方法查找发送消息按钮"""
        try:
            if not self.ui_automation_available or not self.ui_automation:
                self.logger.debug("⚠️ UI Automation 不可用，无法执行替代搜索")
                return None

            self.logger.info("🔍 使用替代UI搜索策略...")

            # 获取根元素
            root_element = self.ui_automation.ElementFromHandle(hwnd)
            if not root_element:
                return None

            # 策略1：只按ClassName查找
            try:
                class_condition = self.ui_automation.CreatePropertyCondition(
                    UIAutomationClient.UIA_ClassNamePropertyId,
                    "mmui::XImage"
                )

                elements = root_element.FindAll(
                    UIAutomationClient.TreeScope_Descendants,
                    class_condition
                )

                if elements and elements.Length > 0:
                    self.logger.debug(f"🔍 找到 {elements.Length} 个 mmui::XImage 元素")

                    # 查找底部区域的元素，应用相同的位置限制
                    for i in range(elements.Length):
                        element = elements.GetElement(i)
                        try:
                            rect = element.CurrentBoundingRectangle
                            element_x = rect.left + (rect.right - rect.left) // 2
                            element_y = rect.top + (rect.bottom - rect.top) // 2

                            # 验证1：必须在底部区域
                            if element_y < 800:
                                continue

                            # 验证2：应用相同的X坐标限制，排除右侧按钮
                            if element_x > 1650:
                                self.logger.debug(f"⚠️ 替代搜索跳过右侧元素: ({element_x}, {element_y})")
                                continue

                            self.logger.info(f"✅ 替代搜索找到合适的底部元素: ({element_x}, {element_y})")
                            return (element_x, element_y)
                        except:
                            continue
            except Exception as e:
                self.logger.debug(f"⚠️ 替代搜索失败: {e}")

            return None

        except Exception as e:
            self.logger.debug(f"⚠️ 替代UI搜索异常: {e}")
            return None

    def get_send_message_button_position(self) -> Tuple[int, int]:
        """获取发送消息按钮的固定位置（界面底部）"""
        try:
            # 重要发现：发送消息按钮位于界面底部固定位置，不随联系人变化
            # 基于UI树信息和截图分析：
            # - X坐标固定：1562 (基于UI树 BoundingRectangle 中心点)
            # - Y坐标固定：在界面底部，不需要根据联系人位置计算

            # 固定坐标（基于UI树信息和界面布局）
            button_x = 1562  # UI树显示的X坐标中心点
            button_y = 924   # 使用UI树中较大的Y坐标值（界面底部）

            self.logger.debug(f"🔍 发送消息按钮固定位置: ({button_x}, {button_y}) - 界面底部")

            return (button_x, button_y)

        except Exception as e:
            self.logger.debug(f"⚠️ 获取发送消息按钮位置失败: {e}")
            # 返回基于UI树的默认底部位置
            return (1562, 924)

    def click_send_message_button(self) -> bool:
        """智能点击发送消息按钮（基于UI Automation）"""
        try:
            self.logger.info("📤 智能定位发送消息按钮...")

            # 获取当前活动的通讯录管理窗口
            hwnd = self.find_contacts_management_window()
            if not hwnd:
                self.logger.error("❌ 无法找到通讯录管理窗口")
                return False

            button_position = None

            # 策略1：使用UI Automation智能查找
            if self.ui_automation:
                self.logger.info("🔍 策略1: 使用UI Automation查找发送消息按钮")
                button_position = self.find_send_message_button_by_ui_automation(hwnd)

                if button_position:
                    self.logger.info(f"✅ UI Automation找到按钮位置: {button_position}")
                else:
                    self.logger.info("⚠️ UI Automation未找到按钮，尝试备选方案")

            # 策略2：替代UI搜索备选方案
            if not button_position:
                self.logger.info("🔍 策略2: 使用替代UI搜索查找发送消息按钮")
                button_position = self.find_send_message_button_by_alternative_ui_search(hwnd)

            # 策略3：固定位置备选方案
            if not button_position:
                self.logger.info("🔍 策略3: 使用固定位置备选方案")
                button_position = self.get_send_message_button_position()

            # 执行点击操作
            if button_position:
                return self._execute_button_click(button_position)
            else:
                self.logger.error("❌ 所有策略都无法找到发送消息按钮")
                return False

        except Exception as e:
            self.logger.error(f"❌ 智能点击发送消息按钮异常: {e}")
            return False

    def _execute_button_click(self, position: Tuple[int, int]) -> bool:
        """执行按钮点击操作"""
        try:
            x, y = position
            self.logger.info(f"📤 点击发送消息按钮: ({x}, {y})")

            # 移动鼠标到按钮位置
            pyautogui.moveTo(x, y, duration=0.15)
            time.sleep(0.1)

            # 点击发送消息按钮
            pyautogui.click(x, y)
            time.sleep(0.3)  # 等待响应

            self.logger.info(f"✅ 成功点击发送消息按钮: ({x}, {y})")
            return True

        except Exception as e:
            self.logger.error(f"❌ 执行按钮点击失败: {e}")
            return False
    
    def find_contacts_management_window(self) -> Optional[int]:
        """查找通讯录管理窗口句柄（增强版）"""
        try:
            self.logger.info("🔍 查找通讯录管理窗口...")

            management_hwnd = None
            all_windows = []  # 用于调试，记录所有窗口

            def enum_windows_callback(hwnd, _):
                nonlocal management_hwnd
                try:
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)

                        # 记录所有窗口用于调试
                        if window_title.strip():  # 只记录有标题的窗口
                            rect = win32gui.GetWindowRect(hwnd)
                            width = rect[2] - rect[0]
                            height = rect[3] - rect[1]
                            all_windows.append(f"'{window_title}' ({width}x{height})")

                        # 精确匹配通讯录管理窗口
                        if "通讯录管理" in window_title:
                            rect = win32gui.GetWindowRect(hwnd)
                            width = rect[2] - rect[0]
                            height = rect[3] - rect[1]

                            self.logger.info(f"🔍 找到通讯录管理窗口: '{window_title}' 句柄: {hwnd} 尺寸: {width}x{height}")

                            # 如果窗口太小，可能是最小化状态，尝试恢复
                            if width < 300 or height < 400:
                                self.logger.warning(f"⚠️ 窗口尺寸过小 ({width}x{height})，尝试恢复窗口...")
                                try:
                                    win32gui.ShowWindow(hwnd, 9)  # SW_RESTORE
                                    time.sleep(0.5)
                                    # 重新获取尺寸
                                    rect = win32gui.GetWindowRect(hwnd)
                                    width = rect[2] - rect[0]
                                    height = rect[3] - rect[1]
                                    self.logger.info(f"🔄 恢复后窗口尺寸: {width}x{height}")
                                except Exception as restore_error:
                                    self.logger.warning(f"⚠️ 恢复窗口失败: {restore_error}")

                            # 降低尺寸要求，或者直接接受找到的窗口
                            if width > 100 and height > 100:
                                management_hwnd = hwnd
                                self.logger.info(f"✅ 接受通讯录管理窗口: '{window_title}' 句柄: {hwnd}")
                                return False

                except Exception as e:
                    self.logger.debug(f"⚠️ 枚举窗口异常: {e}")

                return True

            try:
                win32gui.EnumWindows(enum_windows_callback, None)

                # 如果没找到，输出简化的调试信息
                if not management_hwnd:
                    self.logger.warning("⚠️ 未找到包含'通讯录管理'的窗口")
                    # 只显示可能相关的窗口
                    relevant_windows = [w for w in all_windows if any(keyword in w for keyword in ["通讯录", "联系人", "微信"])]
                    if relevant_windows:
                        self.logger.info("🔍 可能相关的窗口:")
                        for window_info in relevant_windows[:5]:
                            self.logger.info(f"  - {window_info}")

            except Exception as enum_error:
                self.logger.warning(f"⚠️ 窗口枚举异常: {enum_error}")
                # 尝试备选方案：直接查找已知的窗口句柄
                return self._find_window_by_title("通讯录管理")

            return management_hwnd

        except Exception as e:
            self.logger.error(f"❌ 查找通讯录管理窗口失败: {e}")
            return None

    def _find_window_by_title(self, title: str) -> Optional[int]:
        """通过标题查找窗口的备选方案"""
        try:
            hwnd = win32gui.FindWindow(None, title)
            if hwnd and win32gui.IsWindowVisible(hwnd):
                self.logger.info(f"✅ 通过标题找到窗口: '{title}' 句柄: {hwnd}")
                return hwnd
            return None

        except Exception as e:
            self.logger.debug(f"⚠️ 通过标题查找窗口失败: {e}")
            return None

    def list_all_windows(self) -> List[Tuple[int, str, int, int]]:
        """列出所有可见窗口，用于手动选择"""
        windows = []

        def enum_callback(hwnd, _):
            try:
                if win32gui.IsWindowVisible(hwnd):
                    title = win32gui.GetWindowText(hwnd)
                    if title.strip():  # 只记录有标题的窗口
                        rect = win32gui.GetWindowRect(hwnd)
                        width = rect[2] - rect[0]
                        height = rect[3] - rect[1]
                        windows.append((hwnd, title, width, height))
            except:
                pass
            return True

        win32gui.EnumWindows(enum_callback, None)
        return windows

    def _activate_window(self, hwnd: int) -> bool:
        """安全地激活窗口"""
        try:
            self.logger.info(f"🔄 激活窗口 {hwnd}...")

            # 方法1: 尝试直接激活
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
                self.logger.info("✅ 窗口激活成功 (方法1)")
                return True
            except Exception as e1:
                self.logger.debug(f"⚠️ 方法1激活失败: {e1}")

            # 方法2: 尝试显示窗口后激活
            try:
                win32gui.ShowWindow(hwnd, 9)  # SW_RESTORE
                time.sleep(0.2)
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
                self.logger.info("✅ 窗口激活成功 (方法2)")
                return True
            except Exception as e2:
                self.logger.debug(f"⚠️ 方法2激活失败: {e2}")

            # 方法3: 尝试点击窗口来激活
            try:
                rect = win32gui.GetWindowRect(hwnd)
                center_x = (rect[0] + rect[2]) // 2
                center_y = (rect[1] + rect[3]) // 2

                pyautogui.click(center_x, center_y)
                time.sleep(0.5)
                self.logger.info("✅ 窗口激活成功 (方法3-点击)")
                return True
            except Exception as e3:
                self.logger.debug(f"⚠️ 方法3激活失败: {e3}")

            # 如果所有方法都失败，记录警告但继续执行
            self.logger.warning("⚠️ 所有窗口激活方法都失败，但继续执行")
            return True

        except Exception as e:
            self.logger.error(f"❌ 激活窗口异常: {e}")
            return True  # 返回True以继续执行
    
    def calculate_contact_positions(self) -> List[Tuple[int, int]]:
        """计算联系人项目的点击位置"""
        try:
            positions = []
            
            # 计算可见区域内的联系人位置
            start_y = self.contact_area['y']
            end_y = self.contact_area['y'] + self.contact_area['height']
            click_x = self.contact_area['x'] + self.click_offset_x
            
            # 按联系人项目高度计算位置
            current_y = start_y + (self.contact_item_height // 2)  # 第一个联系人的中心
            
            while current_y < end_y:
                positions.append((click_x, current_y))
                current_y += self.contact_item_height
            
            self.logger.info(f"📐 计算出 {len(positions)} 个联系人位置")
            return positions
            
        except Exception as e:
            self.logger.error(f"❌ 计算联系人位置失败: {e}")
            return []
    
    def click_contact_position(self, x: int, y: int, index: int) -> bool:
        """点击指定位置的联系人"""
        try:
            self.logger.info(f"🖱️ 点击联系人位置 {index}: ({x}, {y})")
            
            # 移动鼠标到位置
            pyautogui.moveTo(x, y, duration=0.3)
            time.sleep(0.1)
            
            # 点击
            pyautogui.click(x, y)
            time.sleep(0.8)  # 等待响应
            
            self.logger.info(f"✅ 成功点击位置 {index}: ({x}, {y})")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 点击位置 {index} 失败: {e}")
            return False
    
    def scroll_contact_list(self, direction: str = "down", steps: int = 3, move_mouse: bool = True) -> bool:
        """滚动联系人列表"""
        try:
            # 只有在需要时才移动鼠标到列表中心
            if move_mouse:
                center_x = self.contact_area['x'] + (self.contact_area['width'] // 2)
                center_y = self.contact_area['y'] + (self.contact_area['height'] // 2)
                pyautogui.moveTo(center_x, center_y, duration=0.2)
                time.sleep(0.1)

            # 滚动（保持鼠标在当前位置）
            if direction == "down":
                pyautogui.scroll(-steps)  # 负数向下滚动
                self.logger.info(f"📜 向下滚动 {steps} 步")
            else:
                pyautogui.scroll(steps)   # 正数向上滚动
                self.logger.info(f"📜 向上滚动 {steps} 步")

            time.sleep(0.5)  # 等待滚动完成
            return True

        except Exception as e:
            self.logger.error(f"❌ 滚动列表失败: {e}")
            return False
    
    def click_all_contacts_in_view(self) -> int:
        """点击当前视图中的所有联系人"""
        try:
            positions = self.calculate_contact_positions()
            if not positions:
                self.logger.warning("⚠️ 没有计算出联系人位置")
                return 0
            
            success_count = 0
            
            for i, (x, y) in enumerate(positions, 1):
                self.logger.info(f"📋 点击第 {i}/{len(positions)} 个位置")
                
                if self.click_contact_position(x, y, i):
                    success_count += 1
                    # 点击间隔
                    time.sleep(1.0)
                else:
                    self.logger.warning(f"⚠️ 位置 {i} 点击失败")
            
            self.logger.info(f"✅ 当前视图点击完成: 成功 {success_count}/{len(positions)}")
            return success_count
            
        except Exception as e:
            self.logger.error(f"❌ 点击当前视图联系人失败: {e}")
            return 0
    
    def process_contacts_with_single_scroll(self, max_contacts: int = 100) -> bool:
        """点击一个联系人后立即滚动的处理方式"""
        try:
            self.logger.info("🚀 开始处理联系人（点击一个滚动一次）...")

            # 1. 查找并激活通讯录管理窗口
            management_hwnd = self.find_contacts_management_window()
            if not management_hwnd:
                self.logger.error("❌ 未找到通讯录管理窗口")
                return False

            # 激活窗口
            self._activate_window(management_hwnd)

            # 2. 滚动到顶部（减少滚动次数，避免过度滚动）
            self.logger.info("📜 滚动到列表顶部...")
            for _ in range(3):
                self.scroll_contact_list("up", 3)

            # 3. 逐个处理联系人，每点击一个就滚动
            total_clicks = 0

            # 计算固定点击位置（列表顶部区域的一个固定位置）
            click_x = self.contact_area['x'] + self.click_offset_x
            click_y = self.contact_area['y'] + 100  # 列表顶部下方100px的固定位置

            for contact_index in range(max_contacts):
                self.logger.info(f"📋 处理第 {contact_index + 1}/{max_contacts} 个联系人")

                # 点击固定位置的联系人
                if self.click_contact_position(click_x, click_y, contact_index + 1):
                    total_clicks += 1
                    self.logger.info(f"✅ 成功点击第 {contact_index + 1} 个联系人")

                    # 点击后立即向下滚动，显示下一个联系人（不移动鼠标位置）
                    self.logger.info("📜 向下滚动显示下一个联系人...")
                    self.scroll_contact_list("down", self.scroll_step, move_mouse=False)
                    time.sleep(0.8)  # 等待滚动和加载

                    # 点击发送消息按钮（界面底部固定位置）
                    self.logger.info("📤 点击发送消息按钮...")
                    self.click_send_message_button()
                    time.sleep(0.5)  # 等待发送完成
                else:
                    self.logger.warning(f"⚠️ 第 {contact_index + 1} 个联系人点击失败")
                    # 即使点击失败也滚动，避免卡在同一位置（不移动鼠标位置）
                    self.scroll_contact_list("down", self.scroll_step, move_mouse=False)
                    time.sleep(0.5)

                    # 即使联系人点击失败，也尝试点击发送消息按钮（界面底部固定位置）
                    self.logger.info("📤 尝试点击发送消息按钮...")
                    self.click_send_message_button()
                    time.sleep(0.3)

            self.logger.info(f"🎉 联系人处理完成: 总共点击 {total_clicks} 次")
            return True

        except Exception as e:
            self.logger.error(f"❌ 处理联系人异常: {e}")
            return False

    def process_all_contacts_with_scroll(self, max_scrolls: int = 10) -> bool:
        """通过滚动处理所有联系人（原有方法，保持兼容性）"""
        try:
            self.logger.info("🚀 开始处理所有联系人（包含滚动）...")

            # 1. 查找并激活通讯录管理窗口
            management_hwnd = self.find_contacts_management_window()
            if not management_hwnd:
                self.logger.error("❌ 未找到通讯录管理窗口")
                return False

            # 激活窗口
            self._activate_window(management_hwnd)

            # 2. 滚动到顶部（减少滚动次数，避免过度滚动）
            self.logger.info("📜 滚动到列表顶部...")
            for _ in range(3):
                self.scroll_contact_list("up", 3)

            # 3. 逐页处理联系人
            total_clicks = 0

            for scroll_round in range(max_scrolls):
                self.logger.info(f"📋 处理第 {scroll_round + 1}/{max_scrolls} 页联系人")

                # 点击当前视图的联系人
                clicks_in_view = self.click_all_contacts_in_view()
                total_clicks += clicks_in_view

                if clicks_in_view == 0:
                    self.logger.info("⚠️ 当前视图没有可点击的联系人，可能已到底部")
                    break

                # 滚动到下一页（如果不是最后一轮）
                if scroll_round < max_scrolls - 1:
                    self.logger.info("📜 滚动到下一页...")
                    self.scroll_contact_list("down", self.scroll_step)
                    time.sleep(0.8)  # 等待滚动和加载（优化等待时间）

            self.logger.info(f"🎉 所有联系人处理完成: 总共点击 {total_clicks} 次")
            return True

        except Exception as e:
            self.logger.error(f"❌ 处理所有联系人异常: {e}")
            return False

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('contacts_coordinate_clicker.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🎉 基于坐标的联系人点击脚本")
    print("=" * 60)
    
    try:
        # 创建坐标点击器
        clicker = ContactsCoordinateClicker()
        
        # 显示配置信息
        print("📋 联系人列表区域配置:")
        print(f"  位置: ({clicker.contact_area['x']}, {clicker.contact_area['y']})")
        print(f"  尺寸: {clicker.contact_area['width']} x {clicker.contact_area['height']}")
        print(f"  联系人项目高度: {clicker.contact_item_height}")
        print(f"  点击偏移: {clicker.click_offset_x} (昵称列位置)")
        print(f"  实际点击X坐标: {clicker.contact_area['x'] + clicker.click_offset_x}")
        print("📋 基于UI树信息:")
        print("  昵称文本位置: (1460, 44) -> 偏移 = 1460 - 1400 = 60")
        
        # 自动检测和处理，无需人工确认
        print("\n🔍 自动检测通讯录管理窗口状态...")

        # 先检测窗口是否存在
        test_hwnd = clicker.find_contacts_management_window()
        if not test_hwnd:
            print("❌ 未找到通讯录管理窗口")
            print("\n🔍 显示所有可见窗口，请手动确认:")

            # 列出所有窗口供用户参考
            all_windows = clicker.list_all_windows()
            print("📋 当前所有可见窗口:")
            for i, (hwnd, title, width, height) in enumerate(all_windows[:15], 1):
                print(f"  {i:2d}. '{title}' (句柄:{hwnd}, 尺寸:{width}x{height})")

            if len(all_windows) > 15:
                print(f"  ... 还有 {len(all_windows) - 15} 个窗口")

            print("\n💡 解决方案:")
            print("1. 确保微信已启动")
            print("2. 打开微信通讯录管理窗口")
            print("3. 确认窗口标题包含以下关键词之一:")
            print("   - 通讯录管理、联系人管理、通讯录、微信、WeChat")
            print("4. 重新运行脚本")
            return

        print(f"✅ 检测到通讯录管理窗口 (句柄: {test_hwnd})")

        # 执行联系人处理
        print("\n🚀 开始自动执行联系人点击流程...")
        print("📋 操作流程:")
        print("  1. 激活通讯录管理窗口")
        print("  2. 滚动到列表顶部")
        print("  3. 点击一个联系人")
        print("  4. 立即向下滚动显示下一个联系人")
        print("  5. 点击发送消息按钮")
        print("  6. 重复步骤3-5直到完成")

        result = clicker.process_contacts_with_single_scroll(max_contacts=100)
        
        if result:
            print("\n✅ 联系人点击流程执行成功")
        else:
            print("\n⚠️ 联系人点击流程执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        logger.info("⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"❌ 主程序异常: {e}")
        print(f"\n❌ 程序执行异常: {e}")
    
    print("\n" + "=" * 60)
    print("📋 详细日志已保存到: contacts_coordinate_clicker.log")

if __name__ == "__main__":
    main()
