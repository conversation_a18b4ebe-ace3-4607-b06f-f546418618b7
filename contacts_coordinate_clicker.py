#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于坐标的联系人点击脚本
功能：由于联系人是渲染元素，使用坐标网格点击方式来操作联系人列表
"""

import win32gui
import pyautogui
import time
import logging
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os
from typing import List, Tuple, Optional

class ContactsCoordinateClicker:
    """基于坐标的联系人点击器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 基于UI树信息的联系人列表区
        self.contact_area = {
            'x': 1410,      # 列表左边界
            'y': 76,        # 列表上边界
            'width': 520,   # 列表宽度
            'height': 924   # 列表高度
        }
        
        # 联系人项目配置
        self.contact_item_height = 48  # 每个联系人项目的高度
        self.click_offset_x = 60       # 点击位置相对于左边界的偏移（昵称列位置，基于UI树：1460-1400=60）

        # 滚动步数计算：基于测试结果50-60步对应一个联系人高度(48px)
        # 使用55步作为最佳平衡点，确保精确滚动到下一个联系人
        self.scroll_step = 55         # 单步滚动步数（精确计算，确保滚动一个联系人高度）

        # 固定点击位置配置（单点击模式）
        self.fixed_click_y = self.contact_area['y'] + 200  # 固定Y坐标：列表顶部+200px
        self.single_contact_scroll = 2  # 单个联系人对应的滚动距离

        # 发送消息按钮配置（图像识别）
        # 使用图像识别技术定位发送消息按钮

        # 点击配置
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.2

        # 图像识别配置
        self.debug_dir = "debug_images"  # 调试图像保存目录
        self.template_confidence = 0.7   # 模板匹配置信度阈值
        self.button_detection_area = {    # 按钮检测区域（界面底部）
            'x': 1400,
            'y': 800,
            'width': 600,
            'height': 200
        }

        # 创建调试目录
        if not os.path.exists(self.debug_dir):
            os.makedirs(self.debug_dir)

        self.logger.info("✅ 基于坐标的联系人点击器初始化完成")
        self.logger.info(f"📐 滚动配置: 步数={self.scroll_step}, 联系人高度={self.contact_item_height}px")
        self.logger.info("📤 发送消息按钮: 图像识别定位技术")

    def adjust_scroll_step(self, new_step: int):
        """动态调整滚动步数"""
        old_step = self.scroll_step
        self.scroll_step = new_step
        self.logger.info(f"🔧 滚动步数调整: {old_step} → {new_step}")
        return old_step

    def capture_window_screenshot(self, hwnd: int) -> Optional[np.ndarray]:
        """捕获指定窗口的截图"""
        try:
            # 获取窗口位置和尺寸
            rect = win32gui.GetWindowRect(hwnd)
            x, y, right, bottom = rect
            width = right - x
            height = bottom - y

            self.logger.debug(f"�️ 窗口截图区域: ({x}, {y}) 尺寸: {width}x{height}")

            # 使用PIL截图
            screenshot = pyautogui.screenshot(region=(x, y, width, height))

            # 转换为OpenCV格式
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

            # 保存调试截图
            debug_path = os.path.join(self.debug_dir, f"window_screenshot_{int(time.time())}.png")
            cv2.imwrite(debug_path, screenshot_cv)
            self.logger.debug(f"💾 窗口截图已保存: {debug_path}")

            return screenshot_cv

        except Exception as e:
            self.logger.error(f"❌ 窗口截图失败: {e}")
            return None

    def find_send_message_button_by_image_recognition(self, hwnd: int) -> Optional[Tuple[int, int]]:
        """使用图像识别技术查找发送消息按钮（全窗口检测）"""
        try:
            self.logger.info("🔍 使用图像识别查找发送消息按钮（全窗口检测）...")

            # 1. 捕获完整窗口截图
            screenshot = self.capture_window_screenshot(hwnd)
            if screenshot is None:
                self.logger.error("❌ 无法获取窗口截图")
                return None

            # 2. 直接在整个窗口中检测按钮（不再限制检测区域）
            window_rect = win32gui.GetWindowRect(hwnd)

            # 3. 使用多种方法检测按钮
            button_candidates = self._detect_button_contours(screenshot)
            if not button_candidates:
                self.logger.warning("⚠️ 未检测到按钮候选区域")
                return None

            # 4. 筛选和验证按钮
            best_button = self._validate_button_candidates(button_candidates, window_rect)
            if best_button:
                # 5. 绘制识别结果并保存调试图像
                self._draw_detection_results(screenshot, button_candidates, best_button, window_rect)
                return best_button
            else:
                self.logger.warning("⚠️ 未找到合适的发送消息按钮")
                return None

        except Exception as e:
            self.logger.error(f"❌ 图像识别查找按钮异常: {e}")
            return None



    def _detect_button_contours(self, screenshot: np.ndarray) -> List[dict]:
        """使用多种方法在整个窗口中检测按钮"""
        try:
            height, width = screenshot.shape[:2]
            self.logger.debug(f"🔍 全窗口检测区域尺寸: {width}x{height}")

            # 方法1: 基于文字检测（寻找"发消息"文字区域）
            text_candidates = self._detect_text_buttons(screenshot)

            # 方法2: 基于颜色检测（寻找按钮背景色）
            color_candidates = self._detect_color_buttons(screenshot)

            # 方法3: 基于轮廓检测（改进版）
            contour_candidates = self._detect_contour_buttons(screenshot)

            # 合并所有候选
            all_candidates = text_candidates + color_candidates + contour_candidates

            # 去重和筛选
            filtered_candidates = self._filter_duplicate_candidates(all_candidates)

            self.logger.info(f"🔍 检测到 {len(filtered_candidates)} 个按钮候选区域")
            return filtered_candidates

        except Exception as e:
            self.logger.error(f"❌ 按钮检测失败: {e}")
            return []

    def _detect_text_buttons(self, detection_area: np.ndarray) -> List[dict]:
        """检测包含文字的按钮区域"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(detection_area, cv2.COLOR_BGR2GRAY)

            # 使用形态学操作突出文字区域
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            morph = cv2.morphologyEx(gray, cv2.MORPH_GRADIENT, kernel)

            # 二值化
            _, thresh = cv2.threshold(morph, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 查找轮廓
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            candidates = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)

                # 文字按钮特征：宽度>高度，合理尺寸
                if 40 <= w <= 120 and 20 <= h <= 50 and w > h:
                    candidates.append({
                        'type': 'text',
                        'bbox': (x, y, w, h),
                        'center': (x + w//2, y + h//2),
                        'confidence': 0.8
                    })

            self.logger.debug(f"� 文字检测找到 {len(candidates)} 个候选")
            return candidates

        except Exception as e:
            self.logger.debug(f"⚠️ 文字检测失败: {e}")
            return []

    def _detect_color_buttons(self, detection_area: np.ndarray) -> List[dict]:
        """基于颜色检测按钮区域"""
        try:
            # 转换为HSV颜色空间
            hsv = cv2.cvtColor(detection_area, cv2.COLOR_BGR2HSV)

            # 定义按钮可能的颜色范围（蓝色、绿色等）
            # 蓝色范围（微信常用色）
            blue_lower = np.array([100, 50, 50])
            blue_upper = np.array([130, 255, 255])
            blue_mask = cv2.inRange(hsv, blue_lower, blue_upper)

            # 绿色范围
            green_lower = np.array([40, 50, 50])
            green_upper = np.array([80, 255, 255])
            green_mask = cv2.inRange(hsv, green_lower, green_upper)

            # 合并颜色掩码
            color_mask = cv2.bitwise_or(blue_mask, green_mask)

            # 形态学操作清理噪声
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_CLOSE, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(color_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            candidates = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)

                # 颜色按钮特征：合理尺寸
                if 30 <= w <= 100 and 20 <= h <= 60:
                    candidates.append({
                        'type': 'color',
                        'bbox': (x, y, w, h),
                        'center': (x + w//2, y + h//2),
                        'confidence': 0.6
                    })

            self.logger.debug(f"🎨 颜色检测找到 {len(candidates)} 个候选")
            return candidates

        except Exception as e:
            self.logger.debug(f"⚠️ 颜色检测失败: {e}")
            return []

    def _detect_contour_buttons(self, detection_area: np.ndarray) -> List[dict]:
        """改进的轮廓检测方法"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(detection_area, cv2.COLOR_BGR2GRAY)

            # 使用边缘检测
            edges = cv2.Canny(gray, 50, 150)

            # 形态学操作连接边缘
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            candidates = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)

                # 轮廓按钮特征：矩形形状，合理尺寸
                if 25 <= w <= 80 and 15 <= h <= 50:
                    area = cv2.contourArea(contour)
                    rect_area = w * h
                    if rect_area > 0:
                        area_ratio = area / rect_area
                        if area_ratio > 0.4:  # 较规则的形状
                            candidates.append({
                                'type': 'contour',
                                'bbox': (x, y, w, h),
                                'center': (x + w//2, y + h//2),
                                'confidence': 0.5,
                                'area_ratio': area_ratio
                            })

            self.logger.debug(f"📐 轮廓检测找到 {len(candidates)} 个候选")
            return candidates

        except Exception as e:
            self.logger.debug(f"⚠️ 轮廓检测失败: {e}")
            return []

    def _filter_duplicate_candidates(self, all_candidates: List[dict]) -> List[dict]:
        """去重和筛选候选按钮"""
        try:
            if not all_candidates:
                return []

            # 按置信度排序
            sorted_candidates = sorted(all_candidates, key=lambda x: x.get('confidence', 0.5), reverse=True)

            filtered = []
            for candidate in sorted_candidates:
                x1, y1, w1, h1 = candidate['bbox']
                cx1, cy1 = candidate['center']

                # 检查是否与已有候选重叠
                is_duplicate = False
                for existing in filtered:
                    x2, y2, w2, h2 = existing['bbox']
                    cx2, cy2 = existing['center']

                    # 计算中心点距离
                    distance = ((cx1 - cx2) ** 2 + (cy1 - cy2) ** 2) ** 0.5

                    # 如果距离小于30像素，认为是重复
                    if distance < 30:
                        is_duplicate = True
                        break

                if not is_duplicate:
                    filtered.append(candidate)

            # 限制候选数量
            filtered = filtered[:5]  # 最多保留5个候选

            self.logger.debug(f"🔄 去重后保留 {len(filtered)} 个候选")
            return filtered

        except Exception as e:
            self.logger.debug(f"⚠️ 候选筛选失败: {e}")
            return all_candidates[:3]  # 失败时返回前3个

    def _validate_button_candidates(self, button_candidates: List[dict], window_rect: Tuple[int, int, int, int]) -> Optional[Tuple[int, int]]:
        """验证并选择最佳的按钮候选（针对工具栏优化）"""
        try:
            if not button_candidates:
                self.logger.debug("⚠️ 没有按钮候选")
                return None

            self.logger.info(f"🔍 验证 {len(button_candidates)} 个按钮候选...")

            # 计算窗口信息
            window_x, window_y, window_right, window_bottom = window_rect
            window_width = window_right - window_x
            window_height = window_bottom - window_y

            best_candidate = None
            best_score = -1

            for i, candidate in enumerate(button_candidates):
                bbox = candidate['bbox']
                center = candidate['center']
                candidate_type = candidate.get('type', 'unknown')
                confidence = candidate.get('confidence', 0.5)

                x, y, w, h = bbox
                center_x, center_y = center

                # 转换为全局坐标（相对于屏幕）
                # 现在是全窗口检测，直接转换坐标
                global_x = window_x + center_x
                global_y = window_y + center_y

                score = 0

                # 评分标准1：按钮类型权重
                if candidate_type == 'text':
                    score += 15  # 文字按钮最可能是"发消息"
                elif candidate_type == 'color':
                    score += 10  # 颜色按钮次之
                elif candidate_type == 'contour':
                    score += 5   # 轮廓按钮最后

                # 评分标准2：置信度
                score += int(confidence * 10)

                # 评分标准3：位置合理性（底部区域优先，左侧优先）
                # Y坐标：底部区域优先
                if center_y >= window_height * 0.8:  # 窗口底部20%区域
                    score += 15  # 底部区域最优先
                elif center_y >= window_height * 0.6:  # 窗口下半部分
                    score += 8
                else:
                    score += 2   # 上半部分不太可能

                # X坐标：左侧和中央优先
                if center_x <= window_width // 3:
                    score += 10  # 左侧区域（发消息按钮位置）
                elif center_x <= window_width * 2 // 3:
                    score += 6   # 中央区域
                else:
                    score += 2   # 右侧区域

                # 评分标准4：尺寸合理性（文字按钮应该更宽）
                if candidate_type == 'text' and 40 <= w <= 120 and 20 <= h <= 50:
                    score += 10  # 文字按钮理想尺寸
                elif 25 <= w <= 80 and 15 <= h <= 50:
                    score += 6   # 一般按钮尺寸

                # 评分标准5：避免联系人列表区域（通常在窗口右侧）
                if center_x > window_width * 0.7:  # 右侧30%区域可能是联系人列表
                    score -= 5  # 减分

                self.logger.debug(f"🎯 候选 {i+1}: 类型={candidate_type}, 位置({center_x}, {center_y}), 尺寸({w}x{h}), 评分: {score}")

                if score > best_score:
                    best_score = score
                    best_candidate = {
                        'candidate': candidate,
                        'global_position': (global_x, global_y),
                        'score': score
                    }

            if best_candidate and best_score >= 15:  # 提高最低分数阈值
                pos = best_candidate['global_position']
                candidate_info = best_candidate['candidate']
                self.logger.info(f"✅ 选择最佳按钮: 类型={candidate_info.get('type')}, 全局位置{pos}, 评分{best_score}")
                return pos
            else:
                self.logger.warning(f"⚠️ 没有找到合适的按钮候选 (最高分: {best_score})")
                return None

        except Exception as e:
            self.logger.error(f"❌ 验证按钮候选失败: {e}")
            return None

    def _draw_detection_results(self, screenshot: np.ndarray, button_candidates: List[dict],
                               best_button: Tuple[int, int], window_rect: Tuple[int, int, int, int]) -> None:
        """绘制检测结果并保存调试图像（全窗口检测）"""
        try:
            # 创建结果图像的副本
            result_image = screenshot.copy()

            # 获取窗口尺寸
            height, width = screenshot.shape[:2]

            # 绘制所有候选按钮（不同颜色表示不同类型）
            for i, candidate in enumerate(button_candidates):
                bbox = candidate['bbox']
                candidate_type = candidate.get('type', 'unknown')
                confidence = candidate.get('confidence', 0.5)

                x, y, w, h = bbox
                # 现在是全窗口检测，直接使用坐标
                orig_x = x
                orig_y = y

                # 根据类型选择颜色
                if candidate_type == 'text':
                    color = (0, 255, 0)  # 绿色 - 文字按钮
                    type_label = "T"
                elif candidate_type == 'color':
                    color = (255, 0, 0)  # 蓝色 - 颜色按钮
                    type_label = "C"
                else:
                    color = (0, 255, 255)  # 黄色 - 轮廓按钮
                    type_label = "O"

                # 绘制候选按钮边框
                cv2.rectangle(result_image, (orig_x, orig_y), (orig_x + w, orig_y + h), color, 2)

                # 添加候选信息
                label = f"{type_label}{i+1}({confidence:.1f})"
                cv2.putText(result_image, label, (orig_x, orig_y - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # 绘制最佳按钮（红色圆圈）
            if best_button:
                window_x, window_y, _, _ = window_rect
                # 转换为窗口内坐标
                best_x = best_button[0] - window_x
                best_y = best_button[1] - window_y

                # 绘制最佳按钮标记
                cv2.circle(result_image, (best_x, best_y), 15, (0, 0, 255), 3)
                cv2.putText(result_image, "BEST", (best_x - 20, best_y - 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)

            # 绘制窗口底部区域标记（帮助识别工具栏位置）
            bottom_area_y = int(height * 0.8)  # 底部20%区域
            cv2.line(result_image, (0, bottom_area_y), (width, bottom_area_y), (255, 0, 0), 2)
            cv2.putText(result_image, "Bottom Area (80%)", (10, bottom_area_y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)

            # 添加信息文本
            info_text = [
                f"Candidates: {len(button_candidates)}",
                f"Best Button: {best_button if best_button else 'None'}",
                f"Detection: Full Window ({width}x{height})",
                "Colors: Green=Text, Blue=Color, Yellow=Contour"
            ]

            for i, text in enumerate(info_text):
                cv2.putText(result_image, text, (10, 30 + i * 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

            # 保存结果图像
            result_path = os.path.join(self.debug_dir, f"detection_result_{int(time.time())}.png")
            cv2.imwrite(result_path, result_image)
            self.logger.info(f"💾 检测结果已保存: {result_path}")

        except Exception as e:
            self.logger.error(f"❌ 绘制检测结果失败: {e}")

    def get_fallback_button_position(self, hwnd: int) -> Tuple[int, int]:
        """获取发送消息按钮的备选固定位置"""
        try:
            # 基于窗口尺寸计算相对位置
            window_rect = win32gui.GetWindowRect(hwnd)
            window_x, window_y, window_right, window_bottom = window_rect
            window_width = window_right - window_x
            window_height = window_bottom - window_y

            # 计算按钮位置（窗口底部中央偏右）
            button_x = window_x + int(window_width * 0.75)  # 窗口宽度的75%位置
            button_y = window_bottom - 50  # 窗口底部上方50像素

            self.logger.debug(f"🔍 备选按钮位置: ({button_x}, {button_y}) - 基于窗口相对位置")
            return (button_x, button_y)

        except Exception as e:
            self.logger.debug(f"⚠️ 获取备选按钮位置失败: {e}")
            # 返回默认固定位置
            return (1562, 924)

    def click_send_message_button(self) -> bool:
        """智能点击发送消息按钮（基于图像识别）"""
        try:
            self.logger.info("📤 智能定位发送消息按钮...")

            # 获取当前活动的通讯录管理窗口
            hwnd = self.find_contacts_management_window()
            if not hwnd:
                self.logger.error("❌ 无法找到通讯录管理窗口")
                return False

            button_position = None

            # 策略1：使用图像识别智能查找
            self.logger.info("🔍 策略1: 使用图像识别查找发送消息按钮")
            button_position = self.find_send_message_button_by_image_recognition(hwnd)

            if button_position:
                self.logger.info(f"✅ 图像识别找到按钮位置: {button_position}")
            else:
                self.logger.info("⚠️ 图像识别未找到按钮，尝试备选方案")

            # 策略2：备选固定位置方案
            if not button_position:
                self.logger.info("🔍 策略2: 使用备选固定位置方案")
                button_position = self.get_fallback_button_position(hwnd)

            # 执行点击操作
            if button_position:
                return self._execute_button_click(button_position)
            else:
                self.logger.error("❌ 所有策略都无法找到发送消息按钮")
                return False

        except Exception as e:
            self.logger.error(f"❌ 智能点击发送消息按钮异常: {e}")
            return False

    def _execute_button_click(self, position: Tuple[int, int]) -> bool:
        """执行按钮点击操作"""
        try:
            x, y = position
            self.logger.info(f"📤 点击发送消息按钮: ({x}, {y})")

            # 移动鼠标到按钮位置
            pyautogui.moveTo(x, y, duration=0.15)
            time.sleep(0.1)

            # 点击发送消息按钮
            pyautogui.click(x, y)
            time.sleep(0.3)  # 等待响应

            self.logger.info(f"✅ 成功点击发送消息按钮: ({x}, {y})")
            return True

        except Exception as e:
            self.logger.error(f"❌ 执行按钮点击失败: {e}")
            return False
    
    def find_contacts_management_window(self) -> Optional[int]:
        """查找通讯录管理窗口句柄（增强版）"""
        try:
            self.logger.info("🔍 查找通讯录管理窗口...")

            management_hwnd = None
            all_windows = []  # 用于调试，记录所有窗口

            def enum_windows_callback(hwnd, _):
                nonlocal management_hwnd
                try:
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)

                        # 记录所有窗口用于调试
                        if window_title.strip():  # 只记录有标题的窗口
                            rect = win32gui.GetWindowRect(hwnd)
                            width = rect[2] - rect[0]
                            height = rect[3] - rect[1]
                            all_windows.append(f"'{window_title}' ({width}x{height})")

                        # 精确匹配通讯录管理窗口
                        if "通讯录管理" in window_title:
                            rect = win32gui.GetWindowRect(hwnd)
                            width = rect[2] - rect[0]
                            height = rect[3] - rect[1]

                            self.logger.info(f"🔍 找到通讯录管理窗口: '{window_title}' 句柄: {hwnd} 尺寸: {width}x{height}")

                            # 如果窗口太小，可能是最小化状态，尝试恢复
                            if width < 300 or height < 400:
                                self.logger.warning(f"⚠️ 窗口尺寸过小 ({width}x{height})，尝试恢复窗口...")
                                try:
                                    win32gui.ShowWindow(hwnd, 9)  # SW_RESTORE
                                    time.sleep(0.5)
                                    # 重新获取尺寸
                                    rect = win32gui.GetWindowRect(hwnd)
                                    width = rect[2] - rect[0]
                                    height = rect[3] - rect[1]
                                    self.logger.info(f"🔄 恢复后窗口尺寸: {width}x{height}")
                                except Exception as restore_error:
                                    self.logger.warning(f"⚠️ 恢复窗口失败: {restore_error}")

                            # 降低尺寸要求，或者直接接受找到的窗口
                            if width > 100 and height > 100:
                                management_hwnd = hwnd
                                self.logger.info(f"✅ 接受通讯录管理窗口: '{window_title}' 句柄: {hwnd}")
                                return False

                except Exception as e:
                    self.logger.debug(f"⚠️ 枚举窗口异常: {e}")

                return True

            try:
                win32gui.EnumWindows(enum_windows_callback, None)

                # 如果没找到，输出简化的调试信息
                if not management_hwnd:
                    self.logger.warning("⚠️ 未找到包含'通讯录管理'的窗口")
                    # 只显示可能相关的窗口
                    relevant_windows = [w for w in all_windows if any(keyword in w for keyword in ["通讯录", "联系人", "微信"])]
                    if relevant_windows:
                        self.logger.info("🔍 可能相关的窗口:")
                        for window_info in relevant_windows[:5]:
                            self.logger.info(f"  - {window_info}")

            except Exception as enum_error:
                self.logger.warning(f"⚠️ 窗口枚举异常: {enum_error}")
                # 尝试备选方案：直接查找已知的窗口句柄
                return self._find_window_by_title("通讯录管理")

            return management_hwnd

        except Exception as e:
            self.logger.error(f"❌ 查找通讯录管理窗口失败: {e}")
            return None

    def _find_window_by_title(self, title: str) -> Optional[int]:
        """通过标题查找窗口的备选方案"""
        try:
            hwnd = win32gui.FindWindow(None, title)
            if hwnd and win32gui.IsWindowVisible(hwnd):
                self.logger.info(f"✅ 通过标题找到窗口: '{title}' 句柄: {hwnd}")
                return hwnd
            return None

        except Exception as e:
            self.logger.debug(f"⚠️ 通过标题查找窗口失败: {e}")
            return None

    def list_all_windows(self) -> List[Tuple[int, str, int, int]]:
        """列出所有可见窗口，用于手动选择"""
        windows = []

        def enum_callback(hwnd, _):
            try:
                if win32gui.IsWindowVisible(hwnd):
                    title = win32gui.GetWindowText(hwnd)
                    if title.strip():  # 只记录有标题的窗口
                        rect = win32gui.GetWindowRect(hwnd)
                        width = rect[2] - rect[0]
                        height = rect[3] - rect[1]
                        windows.append((hwnd, title, width, height))
            except:
                pass
            return True

        win32gui.EnumWindows(enum_callback, None)
        return windows

    def _activate_window(self, hwnd: int) -> bool:
        """安全地激活窗口"""
        try:
            self.logger.info(f"🔄 激活窗口 {hwnd}...")

            # 方法1: 尝试直接激活
            try:
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
                self.logger.info("✅ 窗口激活成功 (方法1)")
                return True
            except Exception as e1:
                self.logger.debug(f"⚠️ 方法1激活失败: {e1}")

            # 方法2: 尝试显示窗口后激活
            try:
                win32gui.ShowWindow(hwnd, 9)  # SW_RESTORE
                time.sleep(0.2)
                win32gui.SetForegroundWindow(hwnd)
                time.sleep(0.5)
                self.logger.info("✅ 窗口激活成功 (方法2)")
                return True
            except Exception as e2:
                self.logger.debug(f"⚠️ 方法2激活失败: {e2}")

            # 方法3: 尝试点击窗口来激活
            try:
                rect = win32gui.GetWindowRect(hwnd)
                center_x = (rect[0] + rect[2]) // 2
                center_y = (rect[1] + rect[3]) // 2

                pyautogui.click(center_x, center_y)
                time.sleep(0.5)
                self.logger.info("✅ 窗口激活成功 (方法3-点击)")
                return True
            except Exception as e3:
                self.logger.debug(f"⚠️ 方法3激活失败: {e3}")

            # 如果所有方法都失败，记录警告但继续执行
            self.logger.warning("⚠️ 所有窗口激活方法都失败，但继续执行")
            return True

        except Exception as e:
            self.logger.error(f"❌ 激活窗口异常: {e}")
            return True  # 返回True以继续执行
    
    def calculate_contact_positions(self) -> List[Tuple[int, int]]:
        """计算联系人项目的点击位置"""
        try:
            positions = []
            
            # 计算可见区域内的联系人位置
            start_y = self.contact_area['y']
            end_y = self.contact_area['y'] + self.contact_area['height']
            click_x = self.contact_area['x'] + self.click_offset_x
            
            # 按联系人项目高度计算位置
            current_y = start_y + (self.contact_item_height // 2)  # 第一个联系人的中心
            
            while current_y < end_y:
                positions.append((click_x, current_y))
                current_y += self.contact_item_height
            
            self.logger.info(f"📐 计算出 {len(positions)} 个联系人位置")
            return positions
            
        except Exception as e:
            self.logger.error(f"❌ 计算联系人位置失败: {e}")
            return []
    
    def click_contact_position(self, x: int, y: int, index: int) -> bool:
        """点击指定位置的联系人"""
        try:
            self.logger.info(f"🖱️ 点击联系人位置 {index}: ({x}, {y})")
            
            # 移动鼠标到位置
            pyautogui.moveTo(x, y, duration=0.3)
            time.sleep(0.1)
            
            # 点击
            pyautogui.click(x, y)
            time.sleep(0.8)  # 等待响应
            
            self.logger.info(f"✅ 成功点击位置 {index}: ({x}, {y})")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 点击位置 {index} 失败: {e}")
            return False
    
    def scroll_contact_list(self, direction: str = "down", steps: int = 3, move_mouse: bool = True) -> bool:
        """滚动联系人列表"""
        try:
            # 只有在需要时才移动鼠标到列表中心
            if move_mouse:
                center_x = self.contact_area['x'] + (self.contact_area['width'] // 2)
                center_y = self.contact_area['y'] + (self.contact_area['height'] // 2)
                pyautogui.moveTo(center_x, center_y, duration=0.2)
                time.sleep(0.1)

            # 滚动（保持鼠标在当前位置）
            if direction == "down":
                pyautogui.scroll(-steps)  # 负数向下滚动
                self.logger.info(f"📜 向下滚动 {steps} 步")
            else:
                pyautogui.scroll(steps)   # 正数向上滚动
                self.logger.info(f"📜 向上滚动 {steps} 步")

            time.sleep(0.5)  # 等待滚动完成
            return True

        except Exception as e:
            self.logger.error(f"❌ 滚动列表失败: {e}")
            return False
    
    def click_all_contacts_in_view(self) -> int:
        """点击当前视图中的所有联系人"""
        try:
            positions = self.calculate_contact_positions()
            if not positions:
                self.logger.warning("⚠️ 没有计算出联系人位置")
                return 0
            
            success_count = 0
            
            for i, (x, y) in enumerate(positions, 1):
                self.logger.info(f"📋 点击第 {i}/{len(positions)} 个位置")
                
                if self.click_contact_position(x, y, i):
                    success_count += 1
                    # 点击间隔
                    time.sleep(1.0)
                else:
                    self.logger.warning(f"⚠️ 位置 {i} 点击失败")
            
            self.logger.info(f"✅ 当前视图点击完成: 成功 {success_count}/{len(positions)}")
            return success_count
            
        except Exception as e:
            self.logger.error(f"❌ 点击当前视图联系人失败: {e}")
            return 0
    
    def process_contacts_with_single_scroll(self, max_contacts: int = 100) -> bool:
        """点击一个联系人后立即滚动的处理方式"""
        try:
            self.logger.info("🚀 开始处理联系人（点击一个滚动一次）...")

            # 1. 查找并激活通讯录管理窗口
            management_hwnd = self.find_contacts_management_window()
            if not management_hwnd:
                self.logger.error("❌ 未找到通讯录管理窗口")
                return False

            # 激活窗口
            self._activate_window(management_hwnd)

            # 2. 滚动到顶部（减少滚动次数，避免过度滚动）
            self.logger.info("📜 滚动到列表顶部...")
            for _ in range(3):
                self.scroll_contact_list("up", 3)

            # 3. 逐个处理联系人，每点击一个就滚动
            total_clicks = 0

            # 计算固定点击位置（列表顶部区域的一个固定位置）
            click_x = self.contact_area['x'] + self.click_offset_x
            click_y = self.contact_area['y'] + 100  # 列表顶部下方100px的固定位置

            for contact_index in range(max_contacts):
                self.logger.info(f"📋 处理第 {contact_index + 1}/{max_contacts} 个联系人")

                # 点击固定位置的联系人
                if self.click_contact_position(click_x, click_y, contact_index + 1):
                    total_clicks += 1
                    self.logger.info(f"✅ 成功点击第 {contact_index + 1} 个联系人")

                    # 点击后立即向下滚动，显示下一个联系人（不移动鼠标位置）
                    self.logger.info("📜 向下滚动显示下一个联系人...")
                    self.scroll_contact_list("down", self.scroll_step, move_mouse=False)
                    time.sleep(0.8)  # 等待滚动和加载

                    # 点击发送消息按钮（界面底部固定位置）
                    self.logger.info("📤 点击发送消息按钮...")
                    self.click_send_message_button()
                    time.sleep(0.5)  # 等待发送完成
                else:
                    self.logger.warning(f"⚠️ 第 {contact_index + 1} 个联系人点击失败")
                    # 即使点击失败也滚动，避免卡在同一位置（不移动鼠标位置）
                    self.scroll_contact_list("down", self.scroll_step, move_mouse=False)
                    time.sleep(0.5)

                    # 即使联系人点击失败，也尝试点击发送消息按钮（界面底部固定位置）
                    self.logger.info("📤 尝试点击发送消息按钮...")
                    self.click_send_message_button()
                    time.sleep(0.3)

            self.logger.info(f"🎉 联系人处理完成: 总共点击 {total_clicks} 次")
            return True

        except Exception as e:
            self.logger.error(f"❌ 处理联系人异常: {e}")
            return False

    def process_all_contacts_with_scroll(self, max_scrolls: int = 10) -> bool:
        """通过滚动处理所有联系人（原有方法，保持兼容性）"""
        try:
            self.logger.info("🚀 开始处理所有联系人（包含滚动）...")

            # 1. 查找并激活通讯录管理窗口
            management_hwnd = self.find_contacts_management_window()
            if not management_hwnd:
                self.logger.error("❌ 未找到通讯录管理窗口")
                return False

            # 激活窗口
            self._activate_window(management_hwnd)

            # 2. 滚动到顶部（减少滚动次数，避免过度滚动）
            self.logger.info("📜 滚动到列表顶部...")
            for _ in range(3):
                self.scroll_contact_list("up", 3)

            # 3. 逐页处理联系人
            total_clicks = 0

            for scroll_round in range(max_scrolls):
                self.logger.info(f"📋 处理第 {scroll_round + 1}/{max_scrolls} 页联系人")

                # 点击当前视图的联系人
                clicks_in_view = self.click_all_contacts_in_view()
                total_clicks += clicks_in_view

                if clicks_in_view == 0:
                    self.logger.info("⚠️ 当前视图没有可点击的联系人，可能已到底部")
                    break

                # 滚动到下一页（如果不是最后一轮）
                if scroll_round < max_scrolls - 1:
                    self.logger.info("📜 滚动到下一页...")
                    self.scroll_contact_list("down", self.scroll_step)
                    time.sleep(0.8)  # 等待滚动和加载（优化等待时间）

            self.logger.info(f"🎉 所有联系人处理完成: 总共点击 {total_clicks} 次")
            return True

        except Exception as e:
            self.logger.error(f"❌ 处理所有联系人异常: {e}")
            return False

def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('contacts_coordinate_clicker.log', encoding='utf-8')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    print("🎉 基于坐标的联系人点击脚本")
    print("=" * 60)
    
    try:
        # 创建坐标点击器
        clicker = ContactsCoordinateClicker()
        
        # 显示配置信息
        print("📋 联系人列表区域配置:")
        print(f"  位置: ({clicker.contact_area['x']}, {clicker.contact_area['y']})")
        print(f"  尺寸: {clicker.contact_area['width']} x {clicker.contact_area['height']}")
        print(f"  联系人项目高度: {clicker.contact_item_height}")
        print(f"  点击偏移: {clicker.click_offset_x} (昵称列位置)")
        print(f"  实际点击X坐标: {clicker.contact_area['x'] + clicker.click_offset_x}")
        print("📋 基于UI树信息:")
        print("  昵称文本位置: (1460, 44) -> 偏移 = 1460 - 1400 = 60")
        
        # 自动检测和处理，无需人工确认
        print("\n🔍 自动检测通讯录管理窗口状态...")

        # 先检测窗口是否存在
        test_hwnd = clicker.find_contacts_management_window()
        if not test_hwnd:
            print("❌ 未找到通讯录管理窗口")
            print("\n🔍 显示所有可见窗口，请手动确认:")

            # 列出所有窗口供用户参考
            all_windows = clicker.list_all_windows()
            print("📋 当前所有可见窗口:")
            for i, (hwnd, title, width, height) in enumerate(all_windows[:15], 1):
                print(f"  {i:2d}. '{title}' (句柄:{hwnd}, 尺寸:{width}x{height})")

            if len(all_windows) > 15:
                print(f"  ... 还有 {len(all_windows) - 15} 个窗口")

            print("\n💡 解决方案:")
            print("1. 确保微信已启动")
            print("2. 打开微信通讯录管理窗口")
            print("3. 确认窗口标题包含以下关键词之一:")
            print("   - 通讯录管理、联系人管理、通讯录、微信、WeChat")
            print("4. 重新运行脚本")
            return

        print(f"✅ 检测到通讯录管理窗口 (句柄: {test_hwnd})")

        # 执行联系人处理
        print("\n🚀 开始自动执行联系人点击流程...")
        print("📋 操作流程:")
        print("  1. 激活通讯录管理窗口")
        print("  2. 滚动到列表顶部")
        print("  3. 点击一个联系人")
        print("  4. 立即向下滚动显示下一个联系人")
        print("  5. 点击发送消息按钮")
        print("  6. 重复步骤3-5直到完成")

        result = clicker.process_contacts_with_single_scroll(max_contacts=100)
        
        if result:
            print("\n✅ 联系人点击流程执行成功")
        else:
            print("\n⚠️ 联系人点击流程执行失败")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        logger.info("⚠️ 用户中断操作")
    except Exception as e:
        logger.error(f"❌ 主程序异常: {e}")
        print(f"\n❌ 程序执行异常: {e}")
    
    print("\n" + "=" * 60)
    print("📋 详细日志已保存到: contacts_coordinate_clicker.log")

if __name__ == "__main__":
    main()
